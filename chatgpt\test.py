# import os
# import sys


# sys.path.append(os.getcwd())
# from constants import botManager

# from graia.ariadne.message.chain import MessageChain
# from graia.ariadne.message.element import Plain, Image
# from loguru import logger

# from universal import handle_message

# import asyncio

# from renderer.renderer import MixedContentMessageChainRenderer
# from renderer.merger import BufferedContentMerger
# from renderer.splitter import MultipleSegmentSplitter


# renderer = MultipleSegmentSplitter()

# renderer = BufferedContentMerger(renderer)

# renderer = MixedContentMessageChainRenderer(renderer)


# async def render(text: str):
#     async with renderer:
#         total_text = ''
#         for i in data:
#             await asyncio.sleep(0.03)
#             partial = await renderer.render(i)
#             if partial:
#                 print(partial)
#                 print('---')
#         result = await renderer.result()
#         print('result', result)


# if __name__ == '__main__':
#     async def response(msg):
#         if isinstance(msg, MessageChain):
#             logger.debug(f"Say MessageChain with {len(msg)} items")
#             for elem in msg:
#                 if isinstance(elem, Plain):
#                     logger.debug(f"Say Plain: {elem}")
#                 elif isinstance(elem, Image):
#                     logger.debug(f"Say Image: {elem}")
#         else:
#             logger.debug(f"Say Other: {msg}")
#     asyncio.run(botManager.login())
#     asyncio.run(handle_message(
#         response,
#         f"friend-1234567890",
#         "切换AI bing-c",
#         is_manager=False
#     ))
#     asyncio.run(handle_message(
#         response,
#         f"friend-1234567890",
#         "告诉我全球天气情况",
#         is_manager=False
#     ))

# from universal import is_qiv_key_card
import re

def is_qiv_key_card(string):
    # 定义券码的正则表达式模式
    pattern = r'qiV_(week|mont|quar|year)_[A-Za-z0-9]{15}'
    # 使用正则表达式查找匹配的券码
    match = re.search(pattern, string) # match: <re.Match object; span=(0, 24), match='qiV_mont_8eJBRKDjLR721gK'> 

    # 如果找到匹配的券码，返回券码和 True，否则返回 None 和 False
    if match:
        return match.group(0), True
    else:
        return None, False

x = is_qiv_key_card('qiV_mont_8eJBRKDjLR721gK')[0]

print(is_qiv_key_card('qiv-key-card-1234567890'))
print(x.split('_')[1]) # ['qiV', 'mont', '8eJBRKDjLR721gK']
print(is_qiv_key_card('qiV_week_8eJBRKDjLR721gK'))