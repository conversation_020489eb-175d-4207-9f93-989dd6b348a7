import os
import sys
import datetime
from loguru import logger

def setup_logger():
    """配置日志记录，将日志同时输出到终端和文件"""
    # 获取当前日期
    now = datetime.datetime.now()
    YYYY_MM = now.strftime("%Y-%m")  # 当前年月
    YYYY_MM_DD = now.strftime("%Y-%m-%d")  # 当前年月日

    # 获取当前脚本文件的上一级目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)  # 获取上一级目录

    # 定义日志路径，将日志文件保存在上一级目录的 logs 目录下
    log_dir = os.path.join(parent_dir, "logs", YYYY_MM)
    log_file = os.path.join(log_dir, f"{YYYY_MM_DD}.log")

    # 确保日志目录存在
    os.makedirs(log_dir, exist_ok=True)

    # 确保日志文件存在，如果不存在则创建
    if not os.path.exists(log_file):
        with open(log_file, 'w', encoding='utf-8'):
            pass  # 只创建文件，不做其他操作

    # 清除默认的 logger 配置（避免重复添加 handler）
    logger.remove()

    # 配置日志：同时输出到终端和日志文件
    logger.add(
        log_file, 
        encoding="utf-8", 
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}", 
        level="DEBUG",  # 记录所有级别的日志
        enqueue=True,  # 异步写入日志，避免阻塞
        backtrace=True,  # 记录完整的错误回溯
        diagnose=True    # 提供详细的错误诊断信息
    )

    # 终端输出日志
    logger.add(
        sys.stderr,
        level="DEBUG"
    )

    return logger  # 返回 logger 对象，供其他模块使用
