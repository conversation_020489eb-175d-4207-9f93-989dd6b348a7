from enum import Enum

from config import Config
from manager.bot import BotManager
import os 
from charset_normalizer import from_bytes
import toml

config = Config.load_config()
config.scan_presets()

botManager = BotManager(config)

class cfg_complement():
    def __init__(self):
        # TODO：config py加入追踪后，用作者解读过的cfg信息
        cfg_file_path = "config.cfg"
        with open(cfg_file_path, "rb") as f:
            if guessed_str := from_bytes(f.read()).best():
                cfg_config = dict(toml.loads(str(guessed_str)))

                self.end_juhao = cfg_config.get('prefer','没有配置or解读出来prefer').get('end_with_juhao', True)
                self.segment = cfg_config.get('prefer','没有配置or解读出来prefer').get('segment', True)
                self.segment_symbol = cfg_config.get('prefer','没有配置or解读出来prefer').get('segment_symbol', '。')
                self.action = cfg_config.get('prefer','没有配置or解读出来prefer').get('action_describe', False)
                self.ban_str = cfg_config.get('prefer','没有配置or解读出来prefer').get('ban_word', "【存在风险词汇，请更换话题。】")
            else:
                raise ValueError("无法识别配置文件config.cfg，请检查是否输入有误！")

class LlmName(Enum):
    SlackClaude = "slack-claude"
    PoeSage = "poe-sage"
    PoeGPT4 = "poe-gpt4"
    PoeClaude2 = "poe-claude2"
    PoeClaude = "poe-claude"
    PoeChatGPT = "poe-chatgpt"
    PoeDragonfly = "poe-dragonfly"
    ChatGPT_Web = "chatgpt-web"
    ChatGPT_Api = "chatgpt-api"
    Bing = "bing"
    BingC = "bing-c"
    BingB = "bing-b"
    BingP = "bing-p"
    Bard = "bard"
    YiYan = "yiyan"
    ChatGLM = "chatglm-api"
    CharGLM = "charglm-api"
    Moonshot = "moonshot-api"


class BotPlatform(Enum):
    AriadneBot = "mirai"
    DiscordBot = "discord"
    Onebot = "onebot"
    TelegramBot = "telegram"
    HttpService = "http"
    WecomBot = "wecom"

# 0426 添加违禁词，写在这里是为了启动程序的时候就把违禁词加载到内存，读取更快。
class ProfanityFilter:
    def __init__(self):
        self.ban_file_path = "ban_words"
        self.profanities = []
        for file in os.listdir(self.ban_file_path):
            file_path = os.path.join(self.ban_file_path, file)
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f :
                    self.profanities.append(line.strip())

    def filter_profanities(self, message):
        for profanity in self.profanities:
            if profanity in message:
                message = cfg_complement().ban_str
                # message = message.replace(profanity, '【您的消息有问题】')
        return message

'''
测试时使用 请勿删除
'''
# ban_word = ProfanityFilter()
# print("len ban_word:", len(ban_word.profanities))
# print("ban_word 前20:", ban_word.profanities[0:20])
# xiao_shi_yu_lu = ["你想怎么插", "你想怎么干我", "你有大鸡巴吗", "要做爱吗", "你要插我吗"]

# for guai_hua in xiao_shi_yu_lu:
#     guai_hua = ban_word.filter_profanities(guai_hua)
#     print(guai_hua)
