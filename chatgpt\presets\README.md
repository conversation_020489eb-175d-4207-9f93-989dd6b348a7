# Awesome ChatGPT QQ Presets  

分享一些有意思的 ChatGPT QQ 机器人预设。  

请与[此项目](https://github.com/lss233/chatgpt-mirai-qq-bot)配合使用。  

## 📕 索引

* [角色扮演类](./role-play/)
* [工具类](./assistant/)
* [其他类型](./utils/)

## 🦌 如何使用？

你可以在这里寻找你想要的预设文件，然后放到 `presets` 文件夹。  

在 `config.cfg` 中为此预设添加关键词，重启后即可使用。  

```properties
[presets.keywords]

# 关键词 <==> 预设文件在你电脑上的位置
"正常" = "presets/assistant/default.txt"
```
## 🖊 编写预设

你可以在这里阅读编写教程：[🐱 预设系统](https://github.com/lss233/chatgpt-mirai-qq-bot/wiki/%F0%9F%90%B1-%E9%A2%84%E8%AE%BE%E7%B3%BB%E7%BB%9F)

## 🌼 分享预设  

我们欢迎所有人分享自己的预设。 
 
如果你有预设想要分享，可以通过 Issue 或者 Pull Request 的方式向本项目添加文件。  

## ❤ 贡献者名单  

<a href="https://github.com/lss233/awesome-chatgpt-qq-presets/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=lss233/awesome-chatgpt-qq-presets" />
</a>

Made with [contrib.rocks](https://contrib.rocks).
