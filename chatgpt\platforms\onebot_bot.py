﻿import re
import time
from base64 import b64decode, b64encode
from typing import Union, Optional

import aiohttp
from aiocqhttp import CQHttp, Event, MessageSegment
from charset_normalizer import from_bytes
from graia.ariadne.message.chain import MessageChain
from graia.ariadne.message.element import Image as GraiaImage, At, Plain, Voice
from graia.ariadne.message.parser.base import DetectPrefix
from graia.broadcast import ExecutionStop
from loguru import logger

import constants
from constants import config, botManager, ProfanityFilter, cfg_complement
from manager.bot import BotManager
from middlewares.ratelimit import manager as ratelimit_manager
from universal import handle_message
import time
import toml
import os 
import datetime
from pymongo import MongoClient
from manager.info_manage import InfoManager
from manager.ratelimit import RateLimitManager
import bson

'''
0713 TODO 也涉及数据库操作，但是目前看起来只有画图额度没有适配。正常购买信息没有问题。
'''
from log_config import setup_logger
logger = setup_logger()



manager_r = RateLimitManager()
manager_i = InfoManager()
bot = CQHttp()
ban_filter = ProfanityFilter()
cfg_c = cfg_complement()

# MongoDB连接信息
conversations_collection = manager_i.collection_conversation  # 创建集合

class MentionMe:
    """At 账号或者提到账号群昵称"""

    def __init__(self, name: Union[bool, str] = True) -> None:
        self.name = name

    async def __call__(self, chain: MessageChain, event: Event) -> Optional[MessageChain]:
        first = chain[0]
        if isinstance(first, At) and first.target == config.onebot.qq:
            return MessageChain(chain.__root__[1:], inline=True).removeprefix(" ")
        elif isinstance(first, Plain):
            member_info = await bot.get_group_member_info(group_id=event.group_id, user_id=config.onebot.qq)
            if member_info.get("nickname") and chain.startswith(member_info.get("nickname")):
                return chain.removeprefix(" ")
        raise ExecutionStop


class Image(GraiaImage):
    async def get_bytes(self) -> bytes:
        """尝试获取消息元素的 bytes, 注意, 你无法获取并不包含 url 且不包含 base64 属性的本元素的 bytes.

        Raises:
            ValueError: 你尝试获取并不包含 url 属性的本元素的 bytes.

        Returns:
            bytes: 元素原始数据
        """
        if self.base64:
            return b64decode(self.base64)
        if not self.url:
            raise ValueError("you should offer a url.")
        async with aiohttp.ClientSession() as session:
            async with session.get(self.url) as response:
                response.raise_for_status()
                data = await response.read()
                self.base64 = b64encode(data).decode("ascii")
                return data


# TODO: use MessageSegment
# https://github.com/nonebot/aiocqhttp/blob/master/docs/common-topics.md
def transform_message_chain(text: str) -> MessageChain:
    pattern = r"\[CQ:(\w+),([^\]]+)\]"
    matches = re.finditer(pattern, text)

    message_classes = {
        "text": Plain,
        "image": Image,
        "at": At,
        # Add more message classes here
    }

    messages = []
    start = 0
    for match in matches:
        cq_type, params_str = match.groups()
        params = dict(re.findall(r"(\w+)=([^,]+)", params_str))
        if message_class := message_classes.get(cq_type):
            text_segment = text[start:match.start()]
            if text_segment and not text_segment.startswith('[CQ:reply,'):
                messages.append(Plain(text_segment))
            if cq_type == "at":
                if params.get('qq') == 'all':
                    continue
                params["target"] = int(params.pop("qq"))
            elem = message_class(**params)
            messages.append(elem)
            start = match.end()
    if text_segment := text[start:]:
        messages.append(Plain(text_segment))

    return MessageChain(*messages)


def transform_from_message_chain(chain: MessageChain):
    result = ''
    for elem in chain:
        if isinstance(elem, (Image, GraiaImage)):
            result = result + MessageSegment.image(f"base64://{elem.base64}")
        elif isinstance(elem, Plain):
            result = result + MessageSegment.text(str(elem))
        elif isinstance(elem, Voice):
            result = result + MessageSegment.record(f"base64://{elem.base64}")
    return result


# def response(event, is_group: bool):
#     async def respond(resp):
#         logger.debug(f"[OneBot] 尝试发送消息：{str(resp)}")
#         # print("* resp:",resp) #* resp: 你好！有什么我可以帮助你的吗？
#         # print("* str(resp):",str(resp)) #* str(resp): 你好！有什么我可以帮助你的吗？
#         try:
#             if not isinstance(resp, MessageChain):
#                 resp = MessageChain(resp)
#             resp = transform_from_message_chain(resp)
#             if config.response.quote and '[CQ:record,file=' not in str(resp):  # skip voice
#                 resp = MessageSegment.reply(event.message_id) + resp
#             return await bot.send(len(str(resp))*0.10, event, resp)
#         except Exception as e:
#             logger.exception(e)
#             logger.warning("原始消息发送失败，尝试通过转发发送")
#             return await bot.call_action(
#                 "send_group_forward_msg" if is_group else "send_private_forward_msg",
#                 group_id=event.group_id,
#                 messages=[
#                     MessageSegment.node_custom(event.self_id, "ChatGPT", resp)
#                 ]
#             )
#     return respond


import asyncio
import re

def has_char(text):

    slient_list = ["……","…", "......", "······", "······.", "......'", "'......'", " ......", "...... "]
    pattern = re.compile(r'[\u4e00-\u9fff]|[a-zA-Z]')
    match = re.search(pattern, text)
    if text in slient_list :
        return True
    if match:
        return True
    else:
        return False
    
# def remove_text_inside_brackets(text):
#         """
#         移除字符串中圆括号内的内容。
#         """
#         # 匹配圆括号、方括号和花括号内的内容，包括括号本身
#         pattern = r'\（.*?\）'
#         # 使用空字符串替换匹配的内容
#         return re.sub(pattern, '', text)

def remove_text_inside_brackets(text):
    """
    移除字符串中圆括号内的内容，包括中文和英文括号。
    """
    # 匹配中文括号（），英文括号（）及其内部的内容，包括括号本身
    pattern = r'[\（\(].*?[\）\)]'
    # 使用空字符串替换匹配的内容
    return re.sub(pattern, '', text)

def response(event, is_group: bool, end_juhao = True, segment = True, action = False):

    
    user_id = str(event.user_id)
    info = manager_i.get_info(_id = user_id)
    end_juhao = cfg_c.end_juhao
    segment = cfg_c.segment
    action = info["custom_action_on"]
    segment_symbol = cfg_c.segment_symbol

    # # TODO：config加入追踪后，用作者解读过的cfg信息 # 0426注释掉了，原本这么写不好。
    # cfg_file_path = "config.cfg"
    # with open(cfg_file_path, "rb") as f:
    #     if guessed_str := from_bytes(f.read()).best():
    #         cfg_config = dict(toml.loads(str(guessed_str)))
    #         # 获取用户偏好
    #         # import ipdb 
    #         # ipdb.set_trace()
    #         end_juhao = cfg_c.end_juhao
    #         segment = cfg_c.segment
    #         action = cfg_c.action

    #         end_juhao = cfg_config.get('prefer','没有配置or解读出来prefer').get('end_with_juhao', True)
    #         segment = cfg_config.get('prefer','没有配置or解读出来prefer').get('segment', True)
    #         action = cfg_config.get('prefer','没有配置or解读出来prefer').get('action_describe', False)
    #     else:
    #         raise ValueError("无法识别配置文件config.cfg，请检查是否输入有误！")

    async def respond(resp):

        str_res = str(resp)
        delay_time = 0.01
        delay = 0
        logger.debug(f"[OneBot] 尝试发送消息：{str_res}")
        
        async def log_to_mongodb(user_id, user_message, ai_response):
            # 构建消息记录
            messages = [{
                "send_message": user_message,
                "receive_message": ai_response,
                "created_at": datetime.datetime.now()  # 当前时间
            }]
            
            # 查找用户的现有对话记录
            existing_conversation = conversations_collection.find_one({"user_id": user_id})

            # 计算新增消息的大小
            new_message_size = len(bson.BSON.encode({"messages": messages}))

            # 定义文档最大大小（16MB）
            MAX_DOCUMENT_SIZE = 16 * 1024 * 1024  # 16 MB

            if existing_conversation:
                # 计算现有文档的大小
                total_size = len(bson.BSON.encode(existing_conversation))
                
                # 如果现有文档的大小已经超过最大限制
                if total_size + new_message_size > MAX_DOCUMENT_SIZE:
                    # 创建一个新的记录
                    conversation = {
                        "user_id": user_id,
                        "created_at": datetime.datetime.now(),
                        "messages": messages,
                        "source": "QQ"  # 标记消息来源
                    }
                    # 插入新的记录
                    conversations_collection.insert_one(conversation)
                else:
                    # 如果文档大小还没超出限制，继续将消息推送到现有记录
                    conversations_collection.update_one(
                        {"user_id": user_id},
                        {"$push": {"messages": {"$each": messages}}}  # 将新消息推送到现有记录
                    )
            else:
                # 如果没有找到记录，则插入新的记录
                conversation = {
                    "user_id": user_id,
                    "created_at": datetime.datetime.now(),
                    "messages": messages,
                    "source": "QQ"  # 标记消息来源
                }
                conversations_collection.insert_one(conversation)  # 存储到MongoDB

        # 如果用户不需要动作描述，则去除
        if action == 0:  
            # 如果全段都是动作描述，则仍然保留动作描述，针对（摸摸）之类的回复
            if str_res[0]!='（' or str_res[-1] != '）':
                str_res = remove_text_inside_brackets(str_res)

        # 分割对话，有两种情况：带句号or不带句号。
        if segment == True:
            res_list = str_res.split(segment_symbol)
            new_list = []
            # 男主说话带句号：
            if end_juhao == True:
                for res in res_list[:-1]:
                    res = res+"。"
                    new_list.append(res)
                # 最后一句话使用原本的标点
                new_list.append(res_list[-1])
            # 男主说话不带句号：
            # TODO 这里有个问题是，如果使用逗号分割，最后一句话还是会有句号。（逗号分割逻辑不完善）
            else:
                new_list = res_list
            save_path_base = 'data/record/'
            save_path = save_path_base + str(event.user_id) + '.txt'
            with open(save_path, 'a+', encoding='utf-8') as save:
                time_now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                print(time_now)
                msg_user = str(time_now) + '\n' + event.sender.get("nickname", "好友") + ':' + event.message + '\n'
                save.write(msg_user)             
            save.close()
            
            for res in new_list:
                #去除换行符
                res = res.strip('\n')
                delay += len(res)*delay_time
                
                try:
                    # 如果出现违禁词，发送违禁提醒，违禁提醒在cfg中prefer中设置。
                    res = ban_filter.filter_profanities(res)
                    # 如果是空消息，则跳过不发送。因为如果发送了会引发exception
                    if not has_char(res):
                        continue
                    if not isinstance(res, MessageChain):
                        res = MessageChain(res)
                    res = transform_from_message_chain(res)
                    if config.response.quote and '[CQ:record,file=' not in res:  # skip voice
                        res = MessageSegment.text(str(res))
                    await bot.send(delay, event, res)
                    await log_to_mongodb(event.user_id, event.message, res[0]['data']['text'])  # 记录到MongoDB
                    with open(save_path, 'a+', encoding='utf-8') as save:
                        time_now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        new = str(time_now) + '\n' + 'BOT' + ':' + res + '\n'
                        msg_bot = new[0]['data']['text']
                        save.write(msg_bot)   
                    save.close()
                    #print('current_path======')
                    #print(os.getcwd())
                except Exception as e:
                    logger.exception(e)
                    logger.warning("原始消息发送失败，尝试通过转发发送")
                    await bot.call_action( 
                        "send_group_forward_msg" if is_group else "send_private_forward_msg",
                        group_id=event.group_id,
                        messages=[
                            MessageSegment.node_custom(event.self_id, "ChatGPT", res)
                        ]
                    )

        # 不分割会话，则末尾一定有句号。
        else:
            res = str_res
            logger.debug(f"[OneBot] 尝试发送消息：{res}")
            delay = len(res)*delay_time
            try:
                if not isinstance(res, MessageChain):
                    res = MessageChain(res)
                res = transform_from_message_chain(res)
                if config.response.quote and '[CQ:record,file=' not in res:  # skip voice
                    res = MessageSegment.text(str(res))
                await bot.send(delay, event, res)
                await log_to_mongodb(event.user_id, event.message, res[0]['data']['text'])  # 记录到MongoDB
            except Exception as e:
                logger.exception(e)
                logger.warning("原始消息发送失败，尝试通过转发发送")
                await bot.call_action( 
                    "send_group_forward_msg" if is_group else "send_private_forward_msg",
                    group_id=event.group_id,
                    messages=[
                        MessageSegment.node_custom(event.self_id, "ChatGPT", res)
                    ]
                )
    return respond

# tst版本里面的函数 供参考
# def response(event, is_group: bool):
#     async def respond(resp):
#         str_res = str(resp)
#         #pattern = r'。|！|？|…|（|）'
#         res_list = str_res.split("。")
#         #res_list = re.split(pattern, str_res)
#         new_list = []
#         for res in res_list[:-1]:
#             res = res+"。"
#             new_list.append(res)
#         new_list.append(res_list[-1])
        
#         '''for str_resp in res_list: 
#             print(str_resp)
            
#             async def task(str_resp): 
#                 res = str(str_resp)
#                 logger.debug(f"[OneBot] 尝试发送消息：{res}")
#                 try:
#                     if not isinstance(res, MessageChain):
#                         res = MessageChain(res)
#                     res = transform_from_message_chain(res)
#                     if config.response.quote and '[CQ:record,file=' not in res:  # skip voice
#                         res = MessageSegment.text(str(res))
#                     asyncio.create_task(bot.send(event, res))
#                 except Exception as e:
#                     logger.exception(e)
#                     logger.warning("原始消息发送失败，尝试通过转发发送")
#                     await bot.call_action( 
#                         "send_group_forward_msg" if is_group else "send_private_forward_msg",
#                         group_id=event.group_id,
#                         messages=[
#                             MessageSegment.node_custom(event.self_id, "ChatGPT", res)
#                         ]
#                     )
#                 await asyncio.sleep(2)
#             asyncio.run(task())'''
#         delay = 0
#         for res in new_list:
#             # res = str(res+"。")
#             #logger.debug(f"[OneBot] 尝试发送消息：{res}")
#             #bot.send(event, res_text)
#             logger.debug(f"[OneBot] 尝试发送消息：{res}")
#             delay += len(res)*0.08
#             try:
#                 if not has_char(res):
#                     continue
#                 if not isinstance(res, MessageChain):
#                     res = MessageChain(res)
#                 res = transform_from_message_chain(res)
#                 if config.response.quote and '[CQ:record,file=' not in res:  # skip voice
#                     # resp = MessageSegment.reply(event.message_id) + resp
#                     res = MessageSegment.text(str(res))
#                 #asyncio.create_task(bot.send(event, res))
#                 # create a task for each bot.send() call and append it to the list
#                 # tasks.append(asyncio.create_task(bot.send(delay, event, res)))
#                 await bot.send(delay, event, res)
#             except Exception as e:
#                 logger.exception(e)
#                 logger.warning("原始消息发送失败，尝试通过转发发送")
#                 await bot.call_action( 
#                     "send_group_forward_msg" if is_group else "send_private_forward_msg",
#                     group_id=event.group_id,
#                     messages=[
#                         MessageSegment.node_custom(event.self_id, "ChatGPT", res)
#                     ]
#                 )
#         # use asyncio.gather() to run the tasks concurrently and get the results in order
#         #for task in tasks:
        
#         # results = await asyncio.gather(*tasks)

#     return respond


FriendTrigger = DetectPrefix(config.trigger.prefix + config.trigger.prefix_friend)


# @bot.on_message('private')
# async def _(event: Event):
#     if event.message.startswith('.'):
#         return
#     chain = transform_message_chain(event.message)
#     try:
#         msg = await FriendTrigger(chain, None)
#     except:
#         logger.debug(f"丢弃私聊消息：{event.message}（原因：不符合触发前缀）")
#         return

#     logger.debug(f"私聊消息：{event.message}")

#     try:
#         await handle_message(
#             response(event, False),
#             f"friend-{event.user_id}",
#             msg.display,
#             chain,
#             is_manager=event.user_id == config.onebot.manager_qq,
#             nickname=event.sender.get("nickname", "好友"),
#             request_from=constants.BotPlatform.Onebot
#         )
#     except Exception as e:
#         logger.exception(e)

@bot.on_message('private')
async def _(event: Event):
    if event.message.startswith('.'):
        return
    chain = transform_message_chain(event.message)
    try:
        msg = await FriendTrigger(chain, None)
    except:
        logger.debug(f"丢弃私聊消息：{event.message}（原因：不符合触发前缀）")
        return
    
    import os
    

    # 获取当前脚本所在的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 获取上一级目录的路径
    parent_dir = os.path.abspath(os.path.join(current_dir, '..'))

    # 要读取的文件路径（假设文件名为 config.cfg）
    cfg_file_path = os.path.join(parent_dir, 'config.cfg')
    save_path_base = 'data/record/'

    with open(cfg_file_path, "rb") as f:
        if guessed_str := from_bytes(f.read()).best():
            permit_config = toml.loads(str(guessed_str))
            # 获取 allowChat 部分的数组
            allow_chat_array = permit_config.get('permit', {}).get('allowChat', [])
            print("allow_chat_array: ", allow_chat_array)
            print((event.user_id == config.onebot.manager_qq))
            # 遍历数字列表并执行 A 函数
            if (event.user_id in allow_chat_array) or (allow_chat_array==[]):
                logger.debug(f"私聊消息：{event.message}")
                try:
                    await handle_message(
                        event,
                        response(event, False),
                        f"friend-{event.user_id}",
                        msg.display,
                        chain,
                        is_manager = (event.user_id == config.onebot.manager_qq),
                        nickname=event.sender.get("nickname", "好友"),
                        request_from=constants.BotPlatform.Onebot
                    )
                        
                except Exception as e:
                    logger.exception(e)
            else:
                logger.debug(f"丢弃私聊消息：{event.message}（原因：用户无私聊权限）")
                return
        else:
            raise ValueError("无法识别配置文件，请检查是否输入有误！")

GroupTrigger = [MentionMe(config.trigger.require_mention != "at"), DetectPrefix(
    config.trigger.prefix + config.trigger.prefix_group)] if config.trigger.require_mention != "none" else [
    DetectPrefix(config.trigger.prefix)]


@bot.on_message('group')
async def _(event: Event):
    if event.message.startswith('.'):
        return
    chain = transform_message_chain(event.message)
    try:
        for it in GroupTrigger:
            chain = await it(chain, event)
    except:
        logger.debug(f"丢弃群聊消息：{event.message}（原因：不符合触发前缀）")
        return

    logger.debug(f"群聊消息：{event.message}")

    await handle_message(
        response(event, True),
        f"group-{event.group_id}",
        chain.display,
        is_manager=event.user_id == config.onebot.manager_qq,
        nickname=event.sender.get("nickname", "群友"),
        request_from=constants.BotPlatform.Onebot
    )


@bot.on_message()
async def _(event: Event):
    if event.message != ".重新加载配置文件":
        return
    if event.user_id != config.onebot.manager_qq:
        return await bot.send(event, "您没有权限执行这个操作")
    constants.config = config.load_config()
    config.scan_presets()
    await bot.send(event, "配置文件重新载入完毕！")
    await bot.send(event, "重新登录账号中，详情请看控制台日志……")
    constants.botManager = BotManager(config)
    await botManager.login()
    await bot.send(event, "登录结束")


@bot.on_message()
async def _(event: Event):
    pattern = r"\.设置\s+(\w+)\s+(\S+)\s+额度为\s+(\d+)\s+条/小时"
    match = re.match(pattern, event.message.strip())
    if not match:
        return
    if event.user_id != config.onebot.manager_qq:
        return await bot.send(0,event, "您没有权限执行这个操作")
    msg_type, msg_id, rate = match.groups()
    rate = int(rate)

    if msg_type not in ["群组", "好友"]:
        return await bot.send(0,event, "类型异常，仅支持设定【群组】或【好友】的额度")
    if msg_id != '默认' and not msg_id.isdecimal():
        return await bot.send(0,event, "目标异常，仅支持设定【默认】或【指定 QQ（群）号】的额度")
    ratelimit_manager.update(msg_type, msg_id, rate)
    #同时加获得权限的日期
    ratelimit_manager.date_update(msg_type,msg_id)
    return await bot.send(0, event, "额度更新成功！")


@bot.on_message()
async def _(event: Event):
    pattern = r"\.设置\s+(\w+)\s+(\S+)\s+画图额度为\s+(\d+)\s+个/小时"
    match = re.match(pattern, event.message.strip())
    if not match:
        return
    if event.user_id != config.onebot.manager_qq:
        return await bot.send(0,event, "您没有权限执行这个操作")
    msg_type, msg_id, rate = match.groups()
    rate = int(rate)

    if msg_type not in ["群组", "好友"]:
        return await bot.send(0,event, "类型异常，仅支持设定【群组】或【好友】的额度")
    if msg_id != '默认' and not msg_id.isdecimal():
        return await bot.send(0,event, "目标异常，仅支持设定【默认】或【指定 QQ（群）号】的额度")
    ratelimit_manager.update_draw(msg_type, msg_id, rate)
    return await bot.send(0,event, "额度更新成功！")


@bot.on_message()
async def _(event: Event):
    pattern = r"\.查看\s+(\w+)\s+(\S+)\s+的使用情况"
    match = re.match(pattern, event.message.strip())
    if not match:
        return

    msg_type, msg_id = match.groups()

    if msg_type not in ["群组", "好友"]:
        return await bot.send(0,event, "类型异常，仅支持设定【群组】或【好友】的额度")
    if msg_id != '默认' and not msg_id.isdecimal():
        return await bot.send(0,event, "目标异常，仅支持设定【默认】或【指定 QQ（群）号】的额度")
    limit = ratelimit_manager.get_limit(msg_type, msg_id)
    if limit is None:
        return await bot.send(0,event, f"{msg_type} {msg_id} 没有额度限制。")
    usage = ratelimit_manager.get_usage(msg_type, msg_id)
    current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))
    return await bot.send(0, event,
                          f"{msg_type} {msg_id} 的额度使用情况：{limit['rate']}条/小时， 当前已发送：{usage['count']}条消息\n整点重置，当前服务器时间：{current_time}")


@bot.on_message()
async def _(event: Event):
    pattern = r"\.查看\s+(\w+)\s+(\S+)\s+的画图使用情况"
    match = re.match(pattern, event.message.strip())
    if not match:
        return

    msg_type, msg_id = match.groups()

    if msg_type not in ["群组", "好友"]:
        return await bot.send(event, "类型异常，仅支持设定【群组】或【好友】的额度")
    if msg_id != '默认' and not msg_id.isdecimal():
        return await bot.send(event, "目标异常，仅支持设定【默认】或【指定 QQ（群）号】的额度")
    limit = ratelimit_manager.get_draw_limit(msg_type, msg_id)
    if limit is None:
        return await bot.send(event, f"{msg_type} {msg_id} 没有额度限制。")
    usage = ratelimit_manager.get_draw_usage(msg_type, msg_id)
    current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))
    return await bot.send(event,
                          f"{msg_type} {msg_id} 的额度使用情况：{limit['rate']}个图/小时， 当前已绘制：{usage['count']}个图\n整点重置，当前服务器时间：{current_time}")


@bot.on_message()
async def _(event: Event):
    pattern = ".预设列表"
    event.message = str(event.message)
    if event.message.strip() != pattern:
        return

    if config.presets.hide and event.user_id != config.onebot.manager_qq:
        return await bot.send(event, "您没有权限执行这个操作")
    nodes = []
    for keyword, path in config.presets.keywords.items():
        try:
            with open(path, 'rb') as f:
                guessed_str = from_bytes(f.read()).best()
                preset_data = str(guessed_str).replace("\n\n", "\n=========\n")
            answer = f"预设名：{keyword}\n{preset_data}"

            node = MessageSegment.node_custom(event.self_id, "ChatGPT", answer)
            nodes.append(node)
        except Exception as e:
            logger.error(e)

    if not nodes:
        await bot.send(event, "没有查询到任何预设！")
        return
    try:
        if event.group_id:
            await bot.call_action("send_group_forward_msg", group_id=event.group_id, messages=nodes)
        else:
            await bot.call_action("send_private_forward_msg", user_id=event.user_id, messages=nodes)
    except Exception as e:
        logger.exception(e)
        await bot.send(event, "消息发送失败！请在私聊中查看。")


@bot.on_request
async def _(event: Event):
    if config.system.accept_friend_request:
        await bot.call_action(
            action='.handle_quick_operation_async',
            self_id=event.self_id,
            context=event,
            operation={'approve': True}
        )


@bot.on_request
async def _(event: Event):
    if config.system.accept_group_invite:
        await bot.call_action(
            action='.handle_quick_operation_async',
            self_id=event.self_id,
            context=event,
            operation={'approve': True}
        )


@bot.on_startup
async def startup():
    logger.success("启动完毕，接收消息中……")


async def start_task():
    """|coro|
    以异步方式启动
    """
    return await bot.run_task(host=config.onebot.reverse_ws_host, port=config.onebot.reverse_ws_port)
