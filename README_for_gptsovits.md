# GPTSovits-API

## 项目简介

本项目提供了一个基于 Flask 的 API 网关，用于分发文本到语音（TTS）生成请求到多个后端服务。每个后端服务运行在不同的端口上，并使用不同的模型生成语音。通过 Shell 脚本可以批量启动这些后端服务。

---

## API 接口说明

### 请求示例

**Endpoint**  
`POST http://localhost:9000/generate`

**Headers**  
| Key | Value | 必填 |
|-----|-------|-----|
| `Content-Type` | `application/json` | 是 |

**Request Body (JSON)**  
```json
{
    "model_name": "luchen",
    "text": "你好，这是一个测试文本。",
    "text_language": "zh"
}
```

**cURL 示例**  
```shell
curl -X POST "http://localhost:9000/generate" \
     -H "Content-Type: application/json" \
     -d '{"model_name": "luchen", "text": "你好，这是一个测试文本。", "text_language": "zh"}' \
     --output output.wav
```

---

### 参数说明

| 参数名 | 类型 | 必填 | 描述 | 可选值 | 默认值 |
|--------|------|-----|------|--------|--------|
| `model_name` | string | 是 | 要使用的语音模型 | `luchen`, `qisili`, `xiamingxing`, `xiaoyi`, `zhalisu` | - |
| `text` | string | 是 | 要转换为语音的文本内容 | 任意UTF-8文本 | - |
| `text_language` | string | 是 | 文本语言代码 | `zh` (中文), `en` (英文), `ja` (日文) 等 | - |

---

### 响应说明

**成功响应**  
- **Status Code**: `200 OK`
- **Content-Type**: `audio/wav`
- **Body**: 二进制音频流  
- **示例**:
  ```shell
  # 保存为文件
  curl ... --output speech.wav
  ```

**这样请求后就应该会多一个wav文件，里面是正确的音频这样。**

---

## 快速开始

### 1. 启动后端服务

**解释**：这个部分是启动各个语音模型，分别部署在不同端口。相当于多次启动GPTSoVits提供的api.py然后部署不同男主的模型。

使用 `open_apis.sh` 脚本批量启动多个 API 实例，每个实例绑定不同的端口和模型。

首先，需要把open_apis.sh放在GPTSoVits的根目录下面，在AutoDL就是`/root/GPTSoVits/open_apis.sh`位置。

```bash
# 赋予脚本执行权限
chmod +x open_apis.sh

# 运行脚本
./open_apis.sh
```

**脚本功能：**
- 并行启动多个 `api.py` 实例，每个实例使用不同的端口和模型。
- 日志会记录到 `api_start.log` 和 `api_error.log` 中。

### 2. 启动 Flask API 网关

**解释**：这个部分相当于是启动一个到处分发的网关，这样就不需要到处调用不同的接口了，而是一个接口fits all。

运行 `flask_api.py` 启动 API 网关，用于接收请求并分发到对应的后端服务。

`conda activate flask_api`（安装了requests和flask）

```bash
python flask_api.py
```

**网关功能：**
- 监听端口 `9000`，接收 POST 请求。
- 根据请求中的 `model_name` 参数，将请求分发到对应的后端服务。
- 返回生成的音频文件（`audio/wav`）或错误信息。

---

## 模型与端口映射

| 模型名称      | 端口  | 对应的bot代码后端中的info_manager类的self.nanzhu的值(str)|
|---------------|-------|-------|
| lishen        | 9001  |ls|
| lizeyan       | 9002  |lzy|
| luchen        | 9003  |66|
| qinche        | 9004  |qc|
| qisili        | 9005  |77|
| qiyu          | 9006  |qy|
| shenxinghui   | 9007  |sxh|
| xiamingxing   | 9008  |00|
| xiaoyi        | 9009  |11|
| xiayizhou     | 9010  |xyz|
| zhalisu       | 9011  |55|
```
