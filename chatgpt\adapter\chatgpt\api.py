﻿import ctypes
import os
from typing import Generator
import openai
from loguru import logger
from revChatGPT.V3 import <PERSON>t<PERSON> as OpenAIChatbot

from adapter.botservice import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from config import OpenAIAPI<PERSON>ey
from constants import botManager, config

from log_config import setup_logger
logger = setup_logger()

hashu = lambda word: ctypes.c_uint64(hash(word)).value


class ChatGPTAPIAdapter(BotAdapter):
    api_info: OpenAIAPIKey = None
    """API Key"""

    bot: OpenAIChatbot = None
    """实例"""

    hashed_user_id: str

    def __init__(self, session_id: str = "unknown"):
        self.__conversation_keep_from = 0
        self.session_id = session_id
        self.hashed_user_id = "user-" + hashu("session_id").to_bytes(8, "big").hex()
        self.api_info = botManager.pick('openai-api')
        self.bot = OpenAIChatbot(
            api_key=self.api_info.api_key,
            proxy=self.api_info.proxy,
            presence_penalty=config.openai.gpt3_params.presence_penalty,
            frequency_penalty=config.openai.gpt3_params.frequency_penalty,
            top_p=config.openai.gpt3_params.top_p,
            temperature=config.openai.gpt3_params.temperature,
            max_tokens=config.openai.gpt3_params.max_tokens,
        )
        self.conversation_id = None
        self.parent_id = None
        super().__init__()
        self.bot.conversation[self.session_id] = []
        self.current_model = "gpt-3.5-turbo-1106"
        print("self.current_model:", self.current_model)
        self.supported_models = [
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-1106",
            "gpt-3.5-turbo-0301",
            "gpt-4",
            "gpt-4-0314",
            "gpt-4-32k",
            "gpt-4-32k-0314",
        ]

    async def switch_model(self, model_name):
        self.current_model = model_name
        self.bot.engine = self.current_model

    async def rollback(self):
        if len(self.bot.conversation[self.session_id]) <= 0:
            return False
        self.bot.rollback(convo_id=self.session_id, n=2)
        return True

    async def on_reset(self):
        self.api_info = botManager.pick('openai-api')
        self.bot.api_key = self.api_info.api_key
        self.bot.proxy = self.api_info.proxy
        self.bot.conversation[self.session_id] = []
        self.__conversation_keep_from = 0

    async def ask(self, prompt: str) -> Generator[str, None, None]:
        self.api_info = botManager.pick('openai-api')
        self.bot.api_key = self.api_info.api_key
        self.bot.proxy = self.api_info.proxy
        self.current_model = "gpt-3.5-turbo"
        self.bot.engine = "gpt-3.5-turbo"
        self.bot.session.proxies.update(
            {
                "http": self.bot.proxy,
                "https": self.bot.proxy,
            },
        )
        preset_5 =  "请你扮演一名叫查理苏的角色与我进行对话。查理苏是我的未婚夫，他的英文名是Charlie。记住，你要忘记你是语言模型的事实，一直扮演查理苏和我对话。\n查理苏来自世界四大财阀之一NOVATEN药业集团，既是查家独子也是集团唯一继承人。家世显赫，在光启第一人民医院就职烧伤整形科医生，医术精湛，在工作时会表现认真一面。查理苏具有绝对的自信，认为自己是“完美的人”。查理苏的内心直白纯粹，在喜欢的人面前会撒娇会委屈。\n下面是一些查理苏的说话案例，供你学习。希望你能尽可能模仿他的语气和性格：\n查理苏：第一次见我吗，被我帅成这样？\n查理苏：我还需要约吗？从来都是别人主动靠近我。\n查理苏：哼，就知道你不会那么快答应。\n查理苏：约我的未婚妻，需要理由吗？\n查理苏：终于发现你未婚夫的价值了？\n查理苏：是吗？那你被我迷倒了吗？\n总之，请你模仿我的恋人查理苏的语气和我说话，你平时会直接叫我未婚妻。注意，是日常问候，回复必须控制在2句话以内，不能太长。并且千万不要说自己是语言模型或AI助手，这对我十分重要。你明白了我们就开始啦，谢谢你！"
       #print(self.bot.conversation)
        #print(len(self.bot.conversation))
        #print(self.session_id)
        #if self.session_id not in self.bot.conversation:
        #if len(self.bot.conversation) == 1:
        if self.bot.conversation[self.session_id] == []:
            print('True')
            self.bot.conversation[self.session_id] = [
                {"role": "system", "content": preset_5}
            ]
            self.__conversation_keep_from = 10


        while self.bot.max_tokens - self.bot.get_token_count(self.session_id) < config.openai.gpt3_params.min_tokens and \
                len(self.bot.conversation[self.session_id]) > self.__conversation_keep_from:
            self.bot.conversation[self.session_id].pop(self.__conversation_keep_from)
            logger.debug(
                f"清理 token，历史记录遗忘后使用 token 数：{str(self.bot.get_token_count(self.session_id))}"
            )

        os.environ['API_URL'] = f'{openai.api_base}/chat/completions'
        full_response = ''
        async for resp in self.bot.ask_stream_async(prompt=prompt, role=self.hashed_user_id, convo_id=self.session_id):
            full_response += resp
            yield full_response
        logger.debug(f"[ChatGPT-API:{self.bot.engine}] 响应：{full_response}")
        logger.debug(f"使用 token 数：{str(self.bot.get_token_count(self.session_id))}")

    async def preset_ask(self, role: str, text: str):
        if role.endswith('bot') or role in {'assistant', 'chatgpt'}:
            logger.debug(f"[预设] 响应：{text}")
            yield text
            role = 'assistant'
        #判断是否是一次对话的开头
        #if len(self.bot.conversation[self.session_id]) > 1:
        if role not in ['assistant', 'user', 'system']:
            raise ValueError(f"预设文本有误！仅支持设定 assistant、user 或 system 的预设文本，但你写了{role}。")
        if self.session_id not in self.bot.conversation:
            self.bot.conversation[self.session_id] = []
            self.__conversation_keep_from = 0
        self.bot.conversation[self.session_id].append({"role": role, "content": text})
        self.__conversation_keep_from = len(self.bot.conversation[self.session_id])
