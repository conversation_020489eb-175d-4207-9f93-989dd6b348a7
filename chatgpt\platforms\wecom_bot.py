# -*- coding: utf-8 -*-
import json
import threading
import time
import asyncio
import base64
from io import Bytes<PERSON>
from loguru import logger
from pydub import AudioSegment
from quart import Quart, request, abort, make_response

from graia.ariadne.message.chain import Message<PERSON>hain
from graia.ariadne.message.element import Image, Voice
from graia.ariadne.message.element import Plain

from wechatpy.work.crypto import WeChatCrypto
from wechatpy.work.client import WeChatClient
from wechatpy.exceptions import InvalidSignatureException
from wechatpy.work.exceptions import InvalidCorpIdException
from wechatpy.work import parse_message, create_reply

import constants
from constants import config
from universal import handle_message

import bson
from pymongo.errors import BulkWriteError
from pymongo import MongoClient  # 导入MongoDB客户端
import datetime  # 用于生成当前时间
from manager.info_manage import InfoManager
from log_config import setup_logger
logger = setup_logger()

# MongoDB连接信息
conversations_collection = InfoManager().collection_conversation  # 创建集合

CorpId = config.wecom.corp_id
AgentId = config.wecom.agent_id
Secret = config.wecom.secret
TOKEN = config.wecom.token
EncodingAESKey = config.wecom.encoding_aes_key
crypto = WeChatCrypto(TOKEN, EncodingAESKey, CorpId)
client = WeChatClient(CorpId, Secret)
app = Quart(__name__)

lock = threading.Lock()

request_dic = {}

RESPONSE_SUCCESS = "SUCCESS"
RESPONSE_FAILED = "FAILED"
RESPONSE_DONE = "DONE"


class BotRequest:
    def __init__(self, session_id, user_id, username, message, request_time):
        self.session_id: str = session_id
        self.user_id: str = user_id
        self.username: str = username
        self.message: str = message
        self.result: ResponseResult = ResponseResult()
        self.request_time = request_time
        self.done: bool = False
        """请求是否处理完毕"""

    def set_result_status(self, result_status):
        if not self.result:
            self.result = ResponseResult()
        self.result.result_status = result_status

    def append_result(self, result_type, result):
        with lock:
            if result_type == "message":
                self.result.message.append(result)
            elif result_type == "voice":
                self.result.voice.append(result)
            elif result_type == "image":
                self.result.image.append(result)


class ResponseResult:
    def __init__(self, message=None, voice=None, image=None, result_status=RESPONSE_SUCCESS):
        self.result_status = result_status
        self.message = self._ensure_list(message)
        self.voice = self._ensure_list(voice)
        self.image = self._ensure_list(image)

    def _ensure_list(self, value):
        if value is None:
            return []
        elif isinstance(value, list):
            return value
        else:
            return [value]

    def is_empty(self):
        return not self.message and not self.voice and not self.image

    def pop_all(self):
        with lock:
            self.message = []
            self.voice = []
            self.image = []

    def to_json(self):
        return json.dumps({
            'result': self.result_status,
            'message': self.message,
            'voice': self.voice,
            'image': self.image
        })


@app.route("/wechat", methods=["GET", "POST"])
async def wechat():
    signature = request.args.get("msg_signature", "")
    timestamp = request.args.get("timestamp", "")
    nonce = request.args.get("nonce", "")

    if request.method == "GET":
        echo_str = request.args.get("echostr", "")
        try:
            echo_str = crypto.check_signature(
                signature, timestamp, nonce, echo_str)
        except InvalidSignatureException:
            abort(403)
        return echo_str
    else:
        try:
            msg = crypto.decrypt_message(await request.data, signature, timestamp, nonce)
        except (InvalidSignatureException, InvalidCorpIdException):
            abort(403)
        msg = parse_message(msg)
        logger.debug(msg)
        if msg.type == "text":
            reply = create_reply(msg.content, msg).render()

            bot_request = construct_bot_request(msg)
            asyncio.create_task(process_request(bot_request))
            request_dic[bot_request.request_time] = bot_request

            response = await make_response("ok")
            response.status_code = 200
            return response
        else:
            reply = create_reply("Can not handle this for now", msg).render()
        return crypto.encrypt_message(reply, nonce, timestamp)


async def reply(bot_request: BotRequest):
    # client = WeChatClient(CorpId, Secret)
    UserId = bot_request.user_id
    response = bot_request.result.to_json()
    if bot_request.done:
        request_dic.pop(bot_request.request_time)
    else:
        bot_request.result.pop_all()
    logger.debug(
        f"Bot request {bot_request.request_time} response -> \n{response[:100]}")
    
    # 存储对话到MongoDB
    await store_conversation(bot_request)

    if bot_request.result.message:
        for msg in bot_request.result.message:
            result = client.message.send_text(AgentId, UserId, msg)
            logger.debug(f"Send message result -> {result}")
    if bot_request.result.voice:
        for voice in bot_request.result.voice:
            try:
                # 处理音频数据，优先转换为 AMR 格式
                voice_data, media_type = convert_audio_for_wecom(voice)

                if voice_data is None:
                    raise Exception("音频数据处理失败")

                # 根据转换结果选择上传方式
                if media_type == "voice":
                    # AMR 或 WAV 格式，使用语音类型上传
                    voice_id = client.media.upload("voice", voice_data)["media_id"]
                    result = client.message.send_voice(AgentId, UserId, voice_id)
                    logger.debug(f"✅ 语音消息发送成功 -> {result}")
                else:
                    # 原始格式，使用文件类型上传
                    file_id = client.media.upload("file", voice_data)["media_id"]
                    result = client.message.send_file(AgentId, UserId, file_id)
                    logger.debug(f"✅ 语音文件发送成功 -> {result}")

            except Exception as e:
                logger.error(f"❌ 语音发送失败: {e}")
                # 如果语音发送失败，发送文本提示
                fallback_msg = "[语音消息发送失败，请安装 ffmpeg 或检查系统配置]"
                try:
                    result = client.message.send_text(AgentId, UserId, fallback_msg)
                    logger.debug(f"备用消息发送成功 -> {result}")
                except Exception as fallback_e:
                    logger.error(f"备用消息发送也失败: {fallback_e}")
    if bot_request.result.image:
        for image in bot_request.result.image:
            image_id = client.media.upload(
                "image", BytesIO(base64.b64decode(image)))["media_id"]
            result = client.message.send_image(AgentId, UserId, image_id)
            logger.debug(f"Send image result -> {result}")

async def store_conversation(bot_request: BotRequest):
    # 构建消息记录
    messages = [{
        "send_message": bot_request.message,
        "receive_message": msg,
        "created_at": datetime.datetime.now()  # 使用当前的 UTC 时间
    } for msg in bot_request.result.message]

    # 查找用户的现有对话记录
    existing_conversation = conversations_collection.find_one({"user_id": bot_request.user_id})
    
    # 新的文档，最多存储 16MB 的消息
    max_document_size = 16 * 1024 * 1024  # 16MB

    if existing_conversation:
        # 获取现有聊天记录的消息
        current_messages = existing_conversation.get("messages", [])
        
        # 计算当前文档大小
        total_size = len(bson.BSON.encode(existing_conversation))
        
        # 如果当前文档大小加上新消息超过 16MB，分割成新的文档存储
        if total_size + sum(len(str(msg)) for msg in messages) > max_document_size:
            # 创建新的文档来存储新的消息记录
            conversation_id = existing_conversation["_id"]
            part_number = existing_conversation.get("part_number", 1) + 1
            new_conversation = {
                "user_id": bot_request.user_id,
                "conversation_id": conversation_id,
                "part_number": part_number,
                "created_at": datetime.datetime.now(),
                "messages": messages,
                "source": "wechat"
            }
            conversations_collection.insert_one(new_conversation)  # 插入新的分割文档
        else:
            # 如果没有超出大小，直接更新现有文档
            conversations_collection.update_one(
                {"user_id": bot_request.user_id},
                {"$push": {"messages": {"$each": messages}}}
            )
    else:
        # 如果没有找到记录，则插入新的记录
        conversation = {
            "user_id": bot_request.user_id,
            "created_at": datetime.datetime.now(),
            "messages": messages,
            "source": "wechat",  # 标记消息来源
            "part_number": 1  # 初始文档
        }
        conversations_collection.insert_one(conversation)  # 存储到MongoDB

def convert_audio_for_wecom(audio_base64):
    """
    为企业微信准备音频数据
    优先尝试 AMR 转换，支持多种回退方案
    """
    try:
        # 方法1：优先使用 pydub + ffmpeg 进行 AMR 转换
        from pydub import AudioSegment

        audio_data = BytesIO(base64.b64decode(audio_base64))
        amr = BytesIO()

        # 让 pydub 自动检测格式并加载（支持 WAV、FLAC、MP3 等）
        audio = AudioSegment.from_file(audio_data)

        # 转换为企业微信 AMR 要求的格式：8kHz, 单声道
        audio = audio.set_frame_rate(8000).set_channels(1)

        # 导出为 AMR 格式
        audio.export(amr, format="amr", codec="libopencore_amrnb")
        amr.seek(0)

        logger.debug("✅ 音频转换为 AMR 格式成功")
        return amr, "voice"

    except Exception as e1:
        logger.warning(f"⚠️  AMR 转换失败: {e1}")

        try:
            # 方法2：转换为 WAV 格式（企业微信也支持）
            from pydub import AudioSegment

            audio_data = BytesIO(base64.b64decode(audio_base64))

            # 加载音频文件
            audio = AudioSegment.from_file(audio_data)

            # 转换为企业微信兼容的 WAV 格式：8kHz, 单声道, 16-bit
            audio = audio.set_frame_rate(8000).set_channels(1)

            # 导出为 WAV 格式
            output_wav = BytesIO()
            audio.export(output_wav, format="wav")
            output_wav.seek(0)

            logger.debug("✅ 音频转换为 WAV 格式成功")
            return output_wav, "voice"

        except Exception as e2:
            logger.warning(f"⚠️  WAV 转换也失败: {e2}")

            try:
                # 方法3：直接使用原始音频文件
                audio_data = BytesIO(base64.b64decode(audio_base64))
                logger.debug("⚠️  使用原始音频格式")
                return audio_data, "file"

            except Exception as e3:
                logger.error(f"❌ 音频处理完全失败: {e3}")
                return None, None


def clear_request_dict():
    logger.debug("Watch and clean request_dic.")
    while True:
        now = time.time()
        keys_to_delete = []
        for key, bot_request in request_dic.items():
            if now - int(key)/1000 > 600:
                logger.debug(f"Remove time out request -> {key}|{bot_request.session_id}|{bot_request.user_id}"
                             f"|{bot_request.message}")
                keys_to_delete.append(key)
        for key in keys_to_delete:
            request_dic.pop(key)
        time.sleep(60)


def construct_bot_request(data):
    session_id = f"wecom-{str(data.source)}" or "wecom-default_session"
    user_id = data.source
    username = client.user.get(user_id) or "某人"
    message = data.content
    print('username:', username)
    logger.info(f"Get message from {session_id}[{user_id}]:\n{message}")
    with lock:
        bot_request = BotRequest(session_id, user_id, username,
                                 message, str(int(time.time() * 1000)))
    return bot_request


async def process_request(bot_request: BotRequest):
    async def response(msg):
        logger.info(f"Got response msg -> {type(msg)} -> {msg}")
        _resp = msg
        if not isinstance(msg, MessageChain):
            _resp = MessageChain(msg)
        for ele in _resp:
            if isinstance(ele, Plain) and str(ele):
                bot_request.append_result("message", str(ele))
            elif isinstance(ele, Image):
                bot_request.append_result(
                    "image", ele.base64)
            elif isinstance(ele, Voice):
                # mp3
                bot_request.append_result(
                    "voice", ele.base64)
            else:
                logger.warning(
                    f"Unsupported message -> {type(ele)} -> {str(ele)}")
                bot_request.append_result("message", str(ele))
    logger.debug(f"Start to process bot request {bot_request.request_time}.")
    if bot_request.message is None or not str(bot_request.message).strip():
        await response("message 不能为空!")
        bot_request.set_result_status(RESPONSE_FAILED)
    else:
        print(f'session_id:{bot_request.session_id}')
        print(f'message:{bot_request.message}')
        print(f'bot_request.username:{bot_request.username}')
        await handle_message(
            bot_request,
            _respond=response,
            session_id=bot_request.session_id,
            message=bot_request.message,
            nickname=bot_request.username,
            request_from=constants.BotPlatform.WecomBot
        )
        bot_request.set_result_status(RESPONSE_DONE)
    bot_request.done = True
    logger.debug(f"Bot request {bot_request.request_time} done.")
    await reply(bot_request)


async def start_task():
    """|coro|
    以异步方式启动
    """
    threading.Thread(target=clear_request_dict).start()
    return await app.run_task(host=config.wecom.host, port=config.wecom.port, debug=config.wecom.debug)
