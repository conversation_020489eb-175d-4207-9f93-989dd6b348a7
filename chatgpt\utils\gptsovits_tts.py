import os
import aiohttp
import asyncio
from typing import Optional
from loguru import logger
from constants import config
from manager.info_manage import InfoManager
import tempfile
import base64

from log_config import setup_logger
logger = setup_logger()


class GPTSoVITSTTS:
    """GPT-SoVITS TTS 语音合成服务"""
    
    def __init__(self):
        self.base_url = "http://ffb62b94.natappfree.cc"
        self.manager_i = InfoManager()
        
        # 男主名称到模型名称的映射（根据README_for_gptsovits.md）
        self.nanzhu_to_model = {
            'ls': 'lishen',
            'lzy': 'lizeyan', 
            '66': 'luchen',
            'qc': 'qinche',
            '77': 'qisili',
            'qy': 'qiyu',
            'sxh': 'shenxinghui',
            '00': 'xiamingxing',
            '11': 'xiaoyi',
            'xyz': 'xiayizhou',
            '55': 'zhalisu'
        }
        
    def get_model_name(self) -> str:
        """根据当前男主获取对应的语音模型名称"""
        nanzhu = self.manager_i.nanzhu
        model_name = self.nanzhu_to_model.get(nanzhu, 'luchen')  # 默认使用luchen
        logger.debug(f"当前男主: {nanzhu}, 使用语音模型: {model_name}")
        return model_name

    def convert_wav_to_mp3(self, wav_file_path: str, mp3_file_path: str) -> bool:
        """
        将 WAV 文件转换为 MP3 格式

        Args:
            wav_file_path: WAV 文件路径
            mp3_file_path: MP3 文件路径

        Returns:
            bool: 是否转换成功
        """
        try:
            from pydub import AudioSegment

            # 加载 WAV 文件
            audio = AudioSegment.from_wav(wav_file_path)

            # 导出为 MP3
            audio.export(mp3_file_path, format="mp3")

            logger.debug(f"音频格式转换成功: {wav_file_path} -> {mp3_file_path}")
            return True

        except Exception as e:
            logger.warning(f"音频格式转换失败: {e}")
            # 如果转换失败，直接复制原文件
            try:
                with open(wav_file_path, 'rb') as src:
                    with open(mp3_file_path, 'wb') as dst:
                        dst.write(src.read())
                logger.debug(f"音频格式转换失败，使用原始文件: {wav_file_path} -> {mp3_file_path}")
                return True
            except Exception as copy_e:
                logger.error(f"文件复制也失败: {copy_e}")
                return False
        
    async def synthesize_speech(self, text: str, output_file: str, output_format: str = "wav") -> bool:
        """
        合成语音

        Args:
            text: 要合成的文本
            output_file: 输出文件路径
            output_format: 输出格式 ("wav" 或 "mp3")

        Returns:
            bool: 是否成功合成
        """
        try:
            model_name = self.get_model_name()
            
            # 构建请求数据
            data = {
                "model_name": model_name,
                "text": text,
                "text_language": "zh"
            }
            
            # 发送请求到GPT-SoVITS服务
            timeout = aiohttp.ClientTimeout(total=30)  # 30秒超时
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    f"{self.base_url}/generate",
                    json=data,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    
                    if response.status == 200:
                        # 保存音频文件
                        audio_data = await response.read()

                        if output_format.lower() == "mp3":
                            # 如果需要 MP3 格式，先保存为临时 WAV 文件，然后转换
                            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_wav:
                                temp_wav.write(audio_data)
                                temp_wav_path = temp_wav.name

                            # 转换为 MP3
                            if self.convert_wav_to_mp3(temp_wav_path, output_file):
                                os.unlink(temp_wav_path)  # 删除临时文件
                                logger.debug(f"语音合成并转换为MP3成功: {output_file}")
                                return True
                            else:
                                # 转换失败，使用原始 WAV 数据
                                with open(output_file, 'wb') as f:
                                    f.write(audio_data)
                                os.unlink(temp_wav_path)  # 删除临时文件
                                logger.debug(f"语音合成成功，MP3转换失败，使用WAV格式: {output_file}")
                                return True
                        else:
                            # 直接保存为 WAV
                            with open(output_file, 'wb') as f:
                                f.write(audio_data)
                            logger.debug(f"语音合成成功: {output_file}")
                            return True
                    else:
                        logger.error(f"语音合成失败，状态码: {response.status}")
                        return False
                        
        except asyncio.TimeoutError:
            logger.error("语音合成超时")
            return False
        except aiohttp.ClientError as e:
            logger.error(f"语音合成网络错误: {e}")
            return False
        except Exception as e:
            logger.error(f"语音合成异常: {e}")
            return False
            
    async def get_fallback_voice(self, output_file: str) -> bool:
        """
        获取回退语音文件（ceshi.wav）
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            bool: 是否成功复制回退文件
        """
        try:
            # 获取项目根目录下的ceshi.wav文件
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            fallback_file = os.path.join(project_root, "ceshi.wav")
            
            if os.path.exists(fallback_file):
                # 复制文件
                with open(fallback_file, 'rb') as src:
                    with open(output_file, 'wb') as dst:
                        dst.write(src.read())
                logger.debug(f"使用回退语音文件: {fallback_file} -> {output_file}")
                return True
            else:
                logger.error(f"回退语音文件不存在: {fallback_file}")
                return False
                
        except Exception as e:
            logger.error(f"复制回退语音文件失败: {e}")
            return False


# 全局实例
gptsovits_tts_instance = GPTSoVITSTTS()
