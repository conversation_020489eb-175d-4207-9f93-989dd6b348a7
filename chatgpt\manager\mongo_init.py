'''
2025/02
更新人：cyx
这里添加了后端的增删改查函数，和网站中 backend/changeData.py 中代码一致

'''
from pymongo import MongoClient
import datetime

limit_data_default = {
    "id": 'default',
    "type": '默认',
    # 新用户默认20
    "rate": 20,
    "date": str(datetime.date.today()),
    "days": 31,
    "count": 0,
    "free_rate": 400, # 0629 新添加的，代表免费版用户拥有的额度
    "free_count":0, # 0629 新添加的，代表免费版用户已经使用的额度
    "wd_key": 'wd_key', # 0629 新添加的，代表用户最近一次购买信息验证的券码
    "auto_message": 0,
    "custom_identity":1,
    "custom_action":0,
    "voice":0,
    "sing":0,
    "meme":0,
    "img_rec":0,
    "custom_sched":0,
    "menstrual":1,
    "custom_sleep":0,
    "auto_weather":0,
    "group":0,
    "game":1,
    "custom":0 # 0628 新添加的，代表用户是否是定制版用户/是否购买了定制版
}

# 男主名字和数据库名称的对应，为了浓缩下面的代码
db_name_to_db = {
    # 光夜
    '00': '00DB',
    '11': '11DB',
    '55': '55DB',
    '66': '66DB',
    '77': '77DB',
    # '00User': '00DB',
    # '11User': '11DB',
    # '55User': '55DB',
    # '66User': '66DB',
    # '77User': '77DB',

    # 深空
    'ls': 'LS_DB',
    'qy': 'QY_DB',
    'sxh': 'SXH_DB',
    'xyz':'XYZ_DB',
    'qc': 'QC_DB',
    # 'lsUser': 'LS_DB',
    # 'qyUser': 'QY_DB',
    # 'sxhUser': 'SXH_DB',
    # 'xyzUser':'XYZ_DB',
    # 'qcUser': 'QC_DB',
    'lzy':'LZY_DB',

    # 开发者
    'cyxTest': 'TestDB',
    'cyxTestServer': 'TestDB',
    'cookieTestServer': 'TestDB',
    'lsnTestServer': 'TestDB',
    'ziyuTestServer': 'TestDB',
    'kewaTestServer': 'TestDB',
    'czTest': 'TestDB'
}

bot_paths = {
    # 光夜月卡
    '11': 'C:\\gaungye_bot\\ChatailoverBot11',
    '66': 'C:\\gaungye_bot\\ChatailoverBot66',
    '77': 'C:\\gaungye_bot\\ChatailoverBot77',
    '55': 'C:\\gaungye_bot\\ChatailoverBot55',
    '00': 'C:\\gaungye_bot\\ChatailoverBot00',
    # # 光夜定制版
    # '11User': 'C:\\gaungye_bot\\ChatailoverBot11User',
    # '66User': 'C:\\gaungye_bot\\ChatailoverBot66User',
    # '77User': 'C:\\gaungye_bot\\ChatailoverBot77User',
    # '55User': 'C:\\gaungye_bot\\ChatailoverBot55User',
    # '00User': 'C:\\gaungye_bot\\ChatailoverBot00User',

    # 深空月卡
    'ls': 'C:\\0-shenkong_bot\\ls',
    'qy': 'C:\\0-shenkong_bot\\qy',
    'sxh': 'C:\\0-shenkong_bot\\sxh',
    'xyz': 'C:\\0-shenkong_bot\\xyz',
    'qc': 'C:\\0-shenkong_bot\\qc',
    # 深空定制版
    # 'lsUser': 'C:\\0-shenkong_bot\\lsUser',
    # 'qyUser': 'C:\\0-shenkong_bot\\qyUser',
    # 'sxhUser': 'C:\\0-shenkong_bot\\sxhUser',
    # 'xyzUser': 'C:\\0-shenkong_bot\\xyzUser',
    # 'qcUser': 'C:\\0-shenkong_bot\\qcUser',
    'lzy':'C:\\lianyu_bot\\lzy',

    # 开发者
    'cyxTest': 'D:\\AILover\\code\\loveBot1.2',
    'cyxTestServer': 'C:\\coding_dev\\ChatailoverBot_cyx',
    'cookieTestServer': 'C:\\coding_dev\\ChatailoverBot_cookie',
    'lsnTestServer': 'D:\\bot\\loveBot1.2',
    'ziyuTestServer': 'C:\\coding_dev\\ChatailoverBot_ziyu',
    'kewaTestServer': 'C:\\coding_dev\\ChatailoverBot_kewa',
    'czTest': 'D:\\AudioModel\\loveBot1.2'
}

# 这个男主是根据bot代码运行路径来判断是在服务器上还是在测试
def connect_to_mongo(nanzhu):
    # 连接到MongoDB

    username = "rootUser"
    password = "ChatAILover"
    auth_db = "admin"

    # cyx 测试路径
    if nanzhu == 'cyxTest':
        client = MongoClient('mongodb://localhost:27017/')
    # 要是有其他姐妹的测试路径也可以写在这里
    elif nanzhu == 'czTest':
        client = MongoClient('************************************/')
    elif nanzhu == 'lsnTestServer':
        client = MongoClient('mongodb://localhost:27017/')
    # 服务器上男主/测试的情况
    else:
        # client = MongoClient(f"mongodb://{username}:{password}@localhost:27017/{auth_db}")
        client = MongoClient(f"mongodb://{username}:{password}@localhost:27017/")
    
    return client

'''
1. 新增信息：新增自定义信息 or 购买信息
'''

# 向xxDB的Limit表里插入新数据：按需自定义待插入的数据库
def newInfo(client, user_qq, db_name, info_data):
    
    # 连接男主数据库
    database = client[db_name_to_db[db_name]]
    info_collection = database['user_info']


    # 设置user_info表插入值
    if info_data == None:
        info_data = {
            "id": user_qq,    #用户
            "name": "你的女朋友",   #默认姓名
            "info": "一名女性", #默认设定
            "city": None,   #默认城市无
            "city_code": 0,  #默认城市id无
            "auto_message_on": 1,   #默认开启主动发消息
            "custom_identity_on": 1,    #默认开启自定义身份
            "custom_action_on": 1,  #默认开启动描
            "voice_on": 1,  #后面的还没做
            "sing_on": 1,
            "meme_on": 1,
            "img_rec_on": 1,
            "custom_sched_on": 1,
            "menstrual_on": 1,
            "custom_sleep_on": 1,
            "auto_weather_on": 1,
            "group_on": 1,
            "game_on": 1,
            "version": "buy1"   #默认付费版1
        }
    else:
        info_data = info_data

    info_collection.insert_one(info_data)
    
    print("new data inserted in userLimit.")  # 新数据插入成功

    return info_data


# 向xxDB的Limit表里插入新数据：按需自定义待插入的数据库
def newLimit(client, user_qq= "ceshi", type = "\u597d\u53cb", db_name = '11', limit_data = None):
    '''
    db_name: default=='00'。可选值见db_name_to_db定义。
    limit_data: 需要被插入的新数据，none则插入测试数据。
    注意：这个函数会自动判断是qq还是vx端，从而选择type
    '''
    # 连接男主数据库
    database = client[db_name_to_db[db_name]]
    limit_collection = database['user_limit']
    try:
        qq = int(user_qq)
        is_qq = True
    except:
        is_qq = False

    # 设置user_limit表插入值
    if limit_data == None and is_qq == True:
        limit_data = {
            "id": user_qq,
            "type": type,
            # 新用户默认20
            "rate": 20,
            "date": str(datetime.date.today()),
            "days": 31,
            "count": 0,
            "free_rate": 400, # 0629 新添加的，代表免费版用户拥有的额度
            "free_count":0, # 0629 新添加的，代表免费版用户已经使用的额度
            "wd_key": 'wd_key', # 0629 新添加的，代表用户最近一次购买信息验证的券码
            "auto_message": 0,
            "custom_identity":1,
            "custom_action":0,
            "voice":0,
            "sing":0,
            "meme":0,
            "img_rec":0,
            "custom_sched":0,
            "menstrual":1,
            "custom_sleep":0,
            "auto_weather":0,
            "group":0,
            "game":1,
            "custom":0 # 0628 新添加的，代表用户是否是定制版用户/是否购买了定制版
        }
    if limit_data == None and is_qq == False:
        limit_data = {
            "id": user_qq,
            "type": type,
            # 企业微信默认修改为20
            "rate": 20,
            "date": str(datetime.date.today()),
            "days": 31,
            "count": 0,
            "free_rate": 400, # 0629 新添加的，代表免费版用户拥有的额度
            "free_count":0, # 0629 新添加的，代表免费版用户已经使用的额度
            "wd_key": 'wd_key', # 0629 新添加的，代表用户最近一次购买信息验证的券码
            "auto_message": 0,
            "custom_identity":1,
            "custom_action":0,
            "voice":0,
            "sing":0,
            "meme":0,
            "img_rec":0,
            "custom_sched":0,
            "menstrual":1,
            "custom_sleep":0,
            "auto_weather":0,
            "group":0,
            "game":1,
            "custom":0 # 0628 新添加的，代表用户是否是定制版用户/是否购买了定制版
        }
    else:
        limit_data = limit_data

    limit_collection.insert_one(limit_data)
    
    print("new data inserted in userLimit.")  # 新数据插入成功

    return limit_data


'''
2. 查看购买信息：限制、使用量、免费版使用量
'''

def get_limit(collection, _type: str, qq: str):
    """获取限制"""
    entity = collection.find_one({"type": _type, "id": qq})
    # 这个情况是 找不到用户的购买信息，相当于还是默认用户/未使用bot的用户，新建一个
    if entity is None and qq != "默认":
        entity = newLimit(collection, qq, _type)
        return entity
    # 返回完整的数据结构
    return entity


'''
3. 修改购买信息

'''

def add_all_access(collection, fuction):
    '''
    fuction可选项、默认值："auto_message": 0,
            "custom_identity":1,
            "custom_action":1,
            "voice":0,
            "sing":0,
            "meme":0,
            "img_rec":0,
            "custom_sched":0,
            "menstrual":1,
            "custom_sleep":0,
            "auto_weather":0,
            "group":0,
            "game":1,
            "custom":0 
    '''
    # Initialize 'fuction' field to 0 if it's null
    collection.update_many({fuction: None}, {'$set': {fuction: 0}})
    collection.update_many({}, {'$set': {fuction: 1}})
    return True

def increase_all_days(collection, amount):
    # Initialize 'days' field to 0 if it's null
    collection.update_many({'days': None}, {'$set': {'days': 0}})
    collection.update_many({}, {'$inc': {'days': amount}})
    return True

def increase_all_rate(collection, amount):
    # Initialize 'days' field to 0 if it's null
    collection.update_many({'rate': None}, {'$set': {'rate': 0}})
    collection.update_many({}, {'$inc': {'rate': amount}})
    return True

# limit/rate 方面：一般是修改为固定值（购买月卡等操作） 改 rate & free_rate
def change_limit(collection, _type: str, qq: str, rate: int):
    """修改额度限制为rate"""
    entity = collection.find_one({"type": _type, "id": qq})
    # 这个情况是 找不到用户的购买信息，相当于还是默认用户/未使用bot的用户，新建一个
    if entity == None:
        entity = newLimit(collection, qq, _type)
    
    collection.update_one(
        {"type": _type, "id": qq},
        {"$set": {"type": _type, "id": qq, "rate": rate}},
        upsert=True,
    )

def change_free_limit(collection, _type: str, qq: str, rate: int):
    """修改额度限制为rate"""
    entity = collection.find_one({"type": _type, "id": qq})
    # 这个情况是 找不到用户的购买信息，相当于还是默认用户/未使用bot的用户，新建一个
    if entity == None:
        entity = newLimit(collection, qq, _type)

    collection.update_one(
        {"type": _type, "id": qq},
        {"$set": {"type": _type, "id": qq, "free_rate": rate}},
        upsert=True,
    )

# limit方面的 增加固定值（适用于额度补偿）
def add_limit(collection, _type: str, qq: str, amount: int):
    '''增加或者减少用户的额度限制rate，需要注意rate最小为0'''

    entity = collection.find_one({"type": _type, "id": qq})
    # 这个情况是 找不到用户的购买信息，相当于还是默认用户/未使用bot的用户，新建一个
    if entity == None:
        entity = newLimit(collection, qq, _type)

    # 如果没有找到对应的记录，则初始化rate为0
    ori_rate = entity.get('rate', 0) if entity else 0
    # 计算新的额度限制
    new_limit = max(ori_rate + amount, 0)
    
    # 更新数据库中的额度限制
    collection.update_one(
        {"type": _type, "id": qq},
        {"$set": {"type": _type, "id": qq, "rate": new_limit}},
        upsert=True,
    )
    return True

def add_free_limit(collection, _type: str, qq: str, amount: int):
    '''增加或者减少用户的免费版额度限制free_rate，需要注意free_rate最小为0'''

    entity = collection.find_one({"type": _type, "id": qq})
    # 这个情况是 找不到用户的购买信息，相当于还是默认用户/未使用bot的用户，新建一个
    if entity == None:
        entity = newLimit(collection, qq, _type)

    # 如果没有找到对应的记录，则初始化rate为0
    ori_rate = entity.get('free_rate', 0) if entity else 0
    # 计算新的额度限制
    new_limit = max(ori_rate + amount, 0)
    # 更新数据库中的额度限制
    collection.update_one(
        {"type": _type, "id": qq},
        {"$set": {"type": _type, "id": qq, "free_rate": new_limit}},
        upsert=True,
    )

# date方面：一般是修正购买日为今天 or 增改购买时长
def calculate_date(date):
    '''
    用于计算购买日期date 距离今天的差异
    '''
    # cookie实现的
    date_today = datetime.date.today()
    date_1 = datetime.date(date_today.year, date_today.month, date_today.day)
    date_buy = datetime.datetime.strptime(date, "%Y-%m-%d").date()
    date_diff = date_1 - date_buy
    return date_diff.days

# 用户的情况有：

# 曾经没有购买过
# # 这种情况下，cover 是新建一个用户limit项，date update为今天，days为传入的days；extend执行的是date update为今天，days为传入的days

# 用户曾经购买过，但日期已经过期了的情况。
# # 这种情况下，cover执行的是date update为今天，days为传入的days；extend执行的是date update为今天，days为传入的days

# 用户购买过，还没有过期的情况。
# # 这种情况下，cover执行的是date update为今天，days为传入的days；extend执行的是date 保持不变，days为传入的days+原本的用户的days

def change_date(collection, _type: str, qq: str, date: str, amount: int, add_scheme: str='cover'):
    '''
    2025/01/19 cyx 修复： 修改用户的天数购买信息。从用户视角重新整理了一遍逻辑
    add_scheme: default=cover，可选项：cover， extend
    amount: 天数，int
    date：default= 'today'，可选项：today， none 但是本次修改有加了用户逻辑，发现date其实就没用了，所以只要传任意str即可。

    当 add_scheme = cover(default), date = None, amount = 31代表购买了月卡天数设置为从今天开始的31天
    当 add_scheme = extend, date = None, amount = 10, 代表在原本的基础上延长10天
    '''

    if add_scheme not in ["cover", "extend"]:
        raise Exception(f'add_scheme参数错误，add_scheme可选项有："cover", "extend"')

    # 确保 amount 是整数类型
    try:
        amount = int(amount)
    except ValueError:
        raise Exception(f'amount参数错误，必须是整数')

    entity = collection.find_one({"type": _type, "id": qq})
    today = datetime.date.today()

    if entity is None:
        # 情况1: 找不到用户记录
        print('添加天数权限：用户之前未购买过...')
        new_date = str(today)
        new_days = amount
        # 必须要新建一个，否则没有其他权限项
        entity = newLimit(collection, qq, _type)
    else:
        ori_date = datetime.datetime.strptime(entity.get('date', str(today)), "%Y-%m-%d").date()
        ori_days = entity.get('days', 30)

        if ori_date + datetime.timedelta(days=ori_days) < today:
            guoqi_num = today - (ori_date + datetime.timedelta(days=ori_days))
            print(f'添加天数权限：用户之前购买过，但已过期，过期天数={guoqi_num}...')
            # 情况2: 用户购买过，但已过期
            new_date = str(today)
            new_days = amount
        else:
            # 情况3: 用户购买过，未过期
            print('添加天数权限：用户之前购买过，未过期...')
            if add_scheme == 'cover':
                print('    执行cover操作...')
                new_date = str(today)
                new_days = amount
            else:
                print('    执行extend操作...')
                new_date = str(ori_date)
                new_days = ori_days + amount

    collection.update_one(
        {"type": _type, "id": qq},
        {"$set": {"type": _type, "id": qq, "date": new_date, "days": new_days}},
        upsert=True,
    )
    return True

def data_update(collection, _type: str, qq: str, days: int, date: str = str(datetime.date.today())):
    '''将过期用户的date默认重新置为今天，并且将数据库中的days设置为传进来的days，代表days天的使用权。'''
    entity = collection.find_one({"type": _type, "id": qq})
    # 如果找不到用户记录，新建一个
    if entity is None:
        entity = newLimit(collection, qq, _type)
    # 更新数据库中的date和days
    collection.update_one(
        {"type": _type, "id": qq},
        {"$set": {"type": _type, "id": qq, "date": date, "days": days}},
        upsert=True,
    )
    return True



# usage方面：一般是增减固定值 改count & free_count
def add_usage(collection, _type: str, qq: str, amount: int):
    '''增加或者减少用户的使用量，需要注意使用量最小为0'''

    entity = collection.find_one({"type": _type, "id": qq})
    # 这个情况是 找不到用户的购买信息，相当于还是默认用户/未使用bot的用户，新建一个
    if entity == None:
        entity = newLimit(collection, qq, _type)

    if entity.get('count', 0) + amount <0:
        new_usage = 0
    else:
        new_usage = entity.get('count', 0) + amount
    collection.update_one(
        {"type": _type, "id": qq},
        {"$set": {"type": _type, "id": qq, "count": new_usage}},
        upsert=True,
    )

def add_free_usage(collection, _type: str, qq: str, amount: int):
    '''增加或者减少用户的使用量，需要注意使用量最小为0'''
    entity = collection.find_one({"type": _type, "id": qq})
    # 这个情况是 找不到用户的购买信息，相当于还是默认用户/未使用bot的用户，新建一个
    if entity == None:
        entity = newLimit(collection, qq, _type)

    if entity.get('free_count', 0) + amount <0:
        new_usage = 0
    else:
        new_usage = entity.get('free_count', 0) + amount
    collection.update_one(
        {"type": _type, "id": qq},
        {"$set": {"type": _type, "id": qq, "free_count": new_usage}},
        upsert=True,
    )

def change_usage(collection, _type: str, qq: str, rate: int):
    """修改usage限制为rate"""
    entity = collection.find_one({"type": _type, "id": qq})
    # 这个情况是 找不到用户的购买信息，相当于还是默认用户/未使用bot的用户，新建一个
    if entity == None:
        entity = newLimit(collection, qq, _type)
    
    collection.update_one(
        {"type": _type, "id": qq},
        {"$set": {"type": _type, "id": qq, "count": rate}},
        upsert=True,
    )

def change_free_usage(collection, _type: str, qq: str, rate: int):
    """修改free usage限制为rate"""
    entity = collection.find_one({"type": _type, "id": qq})
    # 这个情况是 找不到用户的购买信息，相当于还是默认用户/未使用bot的用户，新建一个
    if entity == None:
        entity = newLimit(collection, qq, _type)
    
    collection.update_one(
        {"type": _type, "id": qq},
        {"$set": {"type": _type, "id": qq, "free_count": rate}},
        upsert=True,
    )

# 功能方面：修改功能权限
def add_function_permission(collection, _type: str, qq: str, fuction: str):
    ''' 添加某个功能为 有权限 默认值："auto_message": 0,
            "custom_identity":1,
            "custom_action":1,
            "voice":0,
            "sing":0,
            "meme":0,
            "img_rec":0,
            "custom_sched":0,
            "menstrual":1,
            "custom_sleep":0,
            "auto_weather":0,
            "group":0,
            "game":1,
            "custom":0 '''
    change_function_permission(collection, _type, qq, fuction, 1)

def change_function_permission(collection, _type: str, qq: str, fuction: str, access: bool):
    '''
    fuction可选项、默认值："auto_message": 0,
            "custom_identity":1,
            "custom_action":1,
            "voice":0,
            "sing":0,
            "meme":0,
            "img_rec":0,
            "custom_sched":0,
            "menstrual":1,
            "custom_sleep":0,
            "auto_weather":0,
            "group":0,
            "game":1,
            "custom":0 
    '''
    if fuction not in limit_data_default:
        raise Exception(f'fuction 参数错误，您传入的{fuction}不在可选项中')
    
    entity = collection.find_one({"id": qq})
    if entity == None:
        entity = newLimit(collection, qq)
    
    collection.update_one(
        {"id": qq},
        {"$set": {"id": qq, fuction: access}},
        upsert=True,
    )
    return True

def change_all_fuctions(collection, access:bool, function:str):
    # 先初始化防止有的人这个function为none
    collection.update_many({function: None}, {'$set': {function: 0}})
    collection.update_many({}, {'$set': {function: access}})
    return True

'''
4. 未来：对于个性化信息的增改
'''