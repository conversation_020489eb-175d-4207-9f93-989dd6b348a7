import ctypes
import os
import re
from typing import Generator
import openai
from loguru import logger
from revChatGPT.V3 import Chatbot as OpenAIChatbot
import math
from adapter.botservice import <PERSON>t<PERSON><PERSON>pter
from constants import botManager, config
import zhipuai
import json
import datetime
from pymongo import MongoClient
from manager.info_manage import InfoManager
from manager.ratelimit import RateLimitManager
from tinydb import TinyDB, Query

from log_config import setup_logger
logger = setup_logger()

hashu = lambda word: ctypes.c_uint64(hash(word)).value
manager_r = RateLimitManager()
manager_i = InfoManager()

class CharGLMAPIAdapter(BotAdapter):

    bot: OpenAIChatbot = None
    """实例"""

    hashed_user_id: str

    def __init__(self, session_id: str = "unknown"):
        super().__init__()
        self.__conversation_keep_from = 0
        self.session_id = session_id
        self.hashed_user_id = "user-" + hashu("session_id").to_bytes(8, "big").hex()
        self.api_info = botManager.pick('charglm-api')
        self.conversation_id = None
        self.parent_id = None
        self.conversation_history = []

    async def rollback(self):
        if len(self.bot.conversation[self.session_id]) <= 0:
            return False
        self.bot.rollback(convo_id=self.session_id, n=2)
        return True

    async def on_reset(self):
        self.api_info = botManager.pick('charglm-api')
        self.conversation_history = []
        self.__conversation_keep_from = 0
        
    '''def load_info(self, user_id, base_path):
        path = base_path + str(user_id) + ".json"
        try:
            with open(path, 'r', encoding='utf-8') as file:
                data = json.load(file)
        except:
            default = base_path + "default.json"
            with open(default, 'r', encoding='utf-8') as file:
                data = json.load(file)
        try:
            name = data["name"]
        except:
            name = '你的女朋友'
        try:
            intro = data["intro"]
        except:
            intro = '我是一名设计师。'
        return name, intro'''
        
    def split_sentence(self, sentence, keyword):
        index = sentence.find(keyword)
        
        if index != -1:
            part1 = sentence[:index].strip()
            part2 = sentence[index + len(keyword):].strip()
            return part1, part2
        else:
            return None
        
    def load_active_modifications(self, current_date=None):
        #mod_path = 'sys/mod.json'
        #self.api_info = botManager.pick('charglm-api')
        bot_name = self.api_info.bot_name
        mod_path = self.api_info.activity_path
        with open(mod_path, 'r', encoding='utf-8') as f:
            mod = json.load(f)
        #print(bot_name)
        #print(mod)
        role_mods = mod.get(bot_name, [])
        active_contents = None
        initiate_message = None
        if current_date is None:
            current_date = datetime.datetime.now()
        for mod in role_mods:
            #print(mod)
            start = datetime.datetime.strptime(mod['start_date'], "%Y-%m-%d %H:%M")
            end = datetime.datetime.strptime(mod['end_date'], "%Y-%m-%d %H:%M")
            if start <= current_date <= end:
                active_contents = mod['content']
                initiate_message = mod['message']
        return active_contents,initiate_message

    def generate_meta(self, name, user_info, user_info_base, bot_info, bot_name):
        p1, p2 = self.split_sentence(user_info_base, '【姓名】')
        p3, p4 = self.split_sentence(p2, '【自我介绍】')
        base_user = p1 + name + p3 + user_info + p4
        #print(prompt)
        active_contents, initiate_message = self.load_active_modifications()
        if active_contents:
            is_mod = True
        else:
            is_mod = False
        meta = {
            "user_info": base_user,
            "bot_info": bot_info,
            "bot_name": bot_name,
            "user_name": name
            }
        
        #print(meta)
        return meta, initiate_message, is_mod, active_contents
    
    def remove_text_inside_brackets(self,text):
        """
        移除字符串中圆括号、方括号和花括号内的内容。
        """
        # 匹配圆括号、方括号和花括号内的内容，包括括号本身
        pattern = r'\（.*?\）'
        # 使用空字符串替换匹配的内容
        return re.sub(pattern, '', text)


    async def ask(self, prompt: str) -> Generator[str, None, None]:
        self.api_info = botManager.pick('charglm-api')
            
        zhipuai.api_key = self.api_info.api_key
        type, user_id = self.split_sentence(self.session_id, '-')
        user_info_base = self.api_info.user_info_base
        bot_info = self.api_info.bot_info
        bot_name = self.api_info.bot_name
        base_path = self.api_info.path_base
        info = manager_i.get_info(user_id)
        user_name = info["name"] if info["name"] is not None else "你的女朋友"
        intro = info["info"] if info["info"] is not None else "一个女生"
        #user = self.load_info(user_id, base_path)
        #user_name, intro = self.load_info(user_id, base_path)
        time_now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
        meta, initiate_message, is_mod, mod = self.generate_meta(user_name, intro, user_info_base, bot_info, bot_name)
        meta["bot_info"] = meta['bot_info'] + '现在时间是' + str(time_now) + '。'
        if prompt == '退出小剧场':
            self.conversation_history = []
            self.conversation_history.append({'role': 'assistant', 'content': ''})
            msg = '已退出小剧场。'
            yield msg
            logger.debug(f"[CharGLM-API]: 响应：{msg}")
            return
        else:
            if is_mod and self.conversation_history == []:
                self.conversation_history.append({'role': 'assistant', 'content': mod})
                self.conversation_history.append({'role': 'assistant', 'content': initiate_message})
            self.conversation_history.append({'role': 'user', 'content': prompt})
            #print(self.conversation_history)
            response = zhipuai.model_api.sse_invoke(
                model="charglm-3",
                meta=meta,
                prompt=self.conversation_history,
                incremental=True
            ) 
            response_msg = ''
            for event in response.events():
                #print(event)
                if event.event == 'add':
                    response_msg += event.data
                elif event.event == 'finish':
                    token = event.meta
                    event_metadata = json.loads(token)
                    token_usage = event_metadata.get('usage')
                    # TODO: 这个total_tokens大于1500则将history前面40轮对话pop
                    total_tokens = token_usage.get('total_tokens')
                    logger.debug(f"使用 token 数：{str(total_tokens)}")
                    if total_tokens > self.api_info.max_tokens:
                        length = len(self.conversation_history)
                        num = math.ceil(length*self.api_info.delete_per)
                        for i in range (0,num):
                            #print(self.conversation_history[0])
                            self.conversation_history.pop(0)
                        logger.debug(f"清理对话token")
                    break
            #print(self.conversation_history)  
            msg = response_msg
            # msg = self.remove_text_inside_brackets(response_msg)
            self.conversation_history.append({'role': 'assistant', 'content': msg})
            #print(response_msg)
            yield msg
            
            logger.debug(f"[CharGLM-API]: 响应：{msg}")
            #logger.debug(f"使用 token 数：{str(self.bot.get_token_count(self.session_id))}")

    async def preset_ask(self, role: str, text: str):
        if role.endswith('bot') or role in {'assistant', 'chatgpt'}:
            logger.debug(f"[预设] 响应：{text}")
            yield text
            role = 'assistant'
        #判断是否是一次对话的开头
        #if len(self.bot.conversation[self.session_id]) > 1:
        if role not in ['assistant', 'user', 'system']:
            raise ValueError(f"预设文本有误！仅支持设定 assistant、user 或 system 的预设文本，但你写了{role}。")
        if self.session_id not in self.bot.conversation:
            self.bot.conversation[self.session_id] = []
            self.__conversation_keep_from = 0
        self.bot.conversation[self.session_id].append({"role": role, "content": text})
        self.__conversation_keep_from = len(self.bot.conversation[self.session_id])
