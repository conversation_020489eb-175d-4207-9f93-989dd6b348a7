import time
from tinydb import TinyDB, Query
from tinydb.operations import increment
from tinydb.table import Document
import datetime
from constants import botManager
from manager.mongo_init import *
from constants import config


'''
本文件用于对 user_limit 数据库进行修改。
相当于是每个男主修改自己对应的mongo数据库
思路：根据文件路径判断男主，从而判断需要修改的数据库
'''
def get_limit_collection(nanzhu):
    client = connect_to_mongo(nanzhu)
    database = client[db_name_to_db[nanzhu]]
    collection = database['user_limit']
    return collection


class RateLimitManager:
    """额度管理器，本类用于实现业务逻辑，后端代码在mongo_init中"""

    def __init__(self):

        # 根据程序路径判断男主、数据库
        current_file_path = __file__
        self.nanzhu = None
        for key in bot_paths:
            value = bot_paths.get(key)
            if value in current_file_path:
                self.nanzhu = key
                break
        if self.nanzhu == None:
            raise Exception(f'ratelimit 中 self.nanzhu参数错误, 请检查mongo_init.py 中男主路径是否和目前代码运行的路径不相符')

        self.client = connect_to_mongo(self.nanzhu)
        print('当前男主：', self.nanzhu)

        # 连接男主数据库
        database = self.client[db_name_to_db[self.nanzhu]]
        self.collection = database['user_limit']

    def update(self, _type: str, _id: str, rate: int):
        """更新额度限制"""
        self.collection.update_one(
            {"type": _type, "id": _id},
            {"$set": {"type": _type, "id": _id, "rate": rate}},
            upsert=True,
        )

    def update_draw(self, _type: str, _id: str, rate: int):
        pass

    def get_limit(self, _type: str, _id: str) -> dict:
        """获取限制"""
        entity = self.collection.find_one({"type": _type, "id": _id})
        # 这个情况是 找不到用户的购买信息，所以应该new一个
        if entity is None and _id != "默认":
            entity = newLimit(self.client, _id, _type, self.nanzhu)
        return entity

    def get_draw_limit(self, _type: str, _id: str) -> Document:
        pass

    def get_draw_usage(self, _type: str, _id: str) -> Document:
        """获取画图使用量"""

        pass

    def get_usage(self, _type: str, _id: str) -> Document:
        """获取使用量"""

        usage = self.collection.find_one({"type": _type, "id": _id})

        # 初始化 - 对于默认用户而言，对他们每个人都需要初始化
        if usage is None:
            usage = newLimit(self.client, user_qq= _id, type = _type, db_name = self.nanzhu, limit_data = None)
        
        # 因为新表中limit 和 usage在一起，所以返回这个购买信息
        return usage
    
    def get_free_usage(self, _type: str, _id: str) -> Document:
        """获取免费使用量 和 上面的普通usage函数完全一样"""
        usage = self.collection.find_one({"type": _type, "id": _id})
        if usage is None:
            usage = newLimit(self.client, user_qq= _id, type = _type, db_name = self.nanzhu, limit_data = None)
        return usage
    
    def get_msgnum(self, _type: str, _id: str) -> Document:
        """获取每十分钟使用量"""

        pass
    
    def date_update(self, _type: str, _id:str):
        """更新付费日期"""

        date = str(datetime.date.today())
        date_and_id = {"id": _id, "date": date, "days": 31}
        existing_record = self.collection.find_one({"id": _id, "type":_type})

        if existing_record:
            # 直接覆盖
            # days = self.calculate_date(existing_record["date"])
            self.collection.update_one(
                {"id": _id, "type":_type},
                {"$set": date_and_id},
            )
        else:
            newLimit(self.client, user_qq= _id, type = _type, db_name = self.nanzhu, limit_data = None)


    def calculate_date(self, date):
        '''
        用于计算购买日期date 距离今天的差异
        '''
        # cookie实现的
        date_today = datetime.date.today()
        date_1 = datetime.date(date_today.year, date_today.month, date_today.day)
        date_buy = datetime.datetime.strptime(date, "%Y-%m-%d").date()
        date_diff = date_1 - date_buy
        return date_diff.days

        # 之前的 比较复杂
        # date_today = datetime.date.today()
        # date_today = str(date_today).split("-")
        # date_1 = datetime.date(int(date_today[0]), int(date_today[1]), int(date_today[2]))
        # date_buy = str(date).split("-")
        # date_2 = datetime.date(int(date_buy[0]), int(date_buy[1]), int(date_buy[2]))
        # date_3 = date_1 - date_2
        # date_3 = str(date_3).split(" ")
        # if date_3[0] == '0:00:00':
        #     res = 0
        # else:
        #     res = int(date_3[0])
        
        # return res
        

    def increment_usage(self, _type, _id, amount):
        """更新使用量"""

        self.get_usage(_type, _id)
        self.collection.update_one(
            {"type": _type, "id": _id}, {"$inc": {"count": amount}}
        )
            
    def increment_free_usage(self, _type, _id):
        """更新免费使用量"""

        self.get_usage(_type, _id)
        self.collection.update_one(
            {"type": _type, "id": _id}, {"$inc": {"free_count": 1}}
        )


    def check_exceed(self, _type: str, _id: str) -> float:
        """检查是否超额，返回 使用量/额度"""

        limit = self.get_limit(_type, _id)
        usage = self.get_usage(_type, _id)

        # 此类型下无限制
        if limit is None:
            return 0

        # 此类型下为禁止
        return 1 if limit['rate'] == 0 else usage['count'] / limit['rate']
    
    def check_free_exceed(self, _type: str, _id: str) -> float:
        """检查是否超额，返回 使用量/额度"""

        # api_info = botManager.pick('chatglm-api')
        # limit = 
        # limit = api_info.free_limit
        usage = self.get_free_usage(_type, _id)
        limit_value = usage['free_rate']

        # 此类型下无限制
        if limit_value is None:
            return 0

        # 此类型下为禁止
        return 1 if limit_value == 0 else usage['free_count'] / limit_value
    
    def check_buy_date(self, _id):
        """检查是否到期"""
        date_buy = self.collection.find_one({"id": _id})
        if date_buy:
            date = date_buy["date"] if date_buy["date"] is not None else str(datetime.date.today())
            days = date_buy["days"] if date_buy["days"] is not None else 31
            # 计算购买日期到几天的时间，即使用时长
            res = self.calculate_date(date)
            # 使用时长 小于 月卡天数
            if res < days:
                #使用未到期
                return 0
            else:
                #使用已到期
                return 1
        else:
            # 未购买
            return -1

    def check_draw_exceed(self, _type: str, _id: str) -> float:
        """检查画图是否超额，返回 使用量/额度"""

        pass
    
    def write_eduk_card(self, qq, nanzhu):
        '''
        写入额度卡信息 # 还未定义请勿使用
        '''
        pass

    def write_card_with_days_rate(self, qq, nanzhu, days, rate):
        '''
        写入自定义卡信息
        '''
        limit_collection = get_limit_collection(nanzhu)

        print(f"Adding {nanzhu} card... days:{days}, rate:{rate}")  # 输出日志
        # 这些函数会处理没有用户信息的情况
        # 添加主动发消息权限
        add_function_permission(limit_collection, _type = '群组', qq=qq, fuction='auto_message')
        # 额度清0
        change_usage(limit_collection, '群组', qq, 0)
        change_date(limit_collection, '群组', qq, 'today', days, 'cover')
        change_limit(limit_collection, '群组', qq, rate)


    def write_month_card(self, qq, nanzhu):
        '''
        写入月卡信息
        '''
        days = config.qiV_card.month_card_days
        rate = config.qiV_card.month_card_rate
        self.write_card_with_days_rate(qq, nanzhu, days, rate)

    def write_year_card(self, qq, nanzhu):
        '''
        写入年卡信息
        '''
        days = config.qiV_card.year_card_days
        rate = config.qiV_card.year_card_rate
        self.write_card_with_days_rate(qq, nanzhu, days, rate)

    def write_week_card(self, qq, nanzhu):
        '''
        写入周卡信息
        '''
        days = config.qiV_card.week_card_days
        rate = config.qiV_card.week_card_rate
        self.write_card_with_days_rate(qq, nanzhu, days, rate)

    def write_quarter_card(self, qq, nanzhu):
        '''
        写入季卡信息
        '''
        days = config.qiV_card.quarter_card_days
        rate = config.qiV_card.quarter_card_rate
        self.write_card_with_days_rate(qq, nanzhu, days, rate)
    
    def write_special_card(self, qq, nanzhu):
        '''
        写入特殊卡信息
        '''
        days = config.qiV_card.spec_card_days
        rate = config.qiV_card.spec_card_rate
        
        limit_collection = get_limit_collection(nanzhu)

        print(f"Adding {nanzhu} special card... days:{days}, rate:{rate}")  # 输出日志
        # 这些函数会处理没有用户信息的情况
        # 添加主动发消息权限
        add_function_permission(limit_collection, _type = '群组', qq=qq, fuction='auto_message')
        change_date(limit_collection, '群组', qq, 'today', days, 'extend')
        add_limit(limit_collection, '群组', qq, rate)

    def migrate_qq_to_vx(self, qiv_id, qq_id, buchang_rate = 0, buchang_days = 0):
        '''
        迁移用户信息，从qq到qvx
        '''
        existing_record = self.collection.find_one({"id": qq_id, "type":'好友'})

        if existing_record:
            # 直接覆盖
            self.collection.update_one(
                {"id": qq_id, "type":'好友'},
                {"$set": {"type": '群组', "id": qiv_id, "rate": existing_record["rate"] + buchang_rate, "days": existing_record["days"] + buchang_days}},
                upsert=True,
            )
            return True
        else:
            return False

        



    
    
