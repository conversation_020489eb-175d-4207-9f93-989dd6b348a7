import time
from tinydb import TinyDB, Query
from tinydb.operations import increment
from tinydb.table import Document
import datetime
from constants import botManager
from manager.mongo_init import *

'''
本文件用于对 user_info 数据库进行修改。
相当于是每个男主修改自己对应的mongo数据库
思路：根据文件路径判断男主，从而判断需要修改的数据库
'''

# 男主名字和数据库名称的对应，为了浓缩下面的代码
# real_name_to_db = {
#     # 光夜
#     '夏鸣星': '00DB',
#     '萧逸': '11DB',
#     '查理苏': '55DB',
#     '陆沉': '66DB',
#     '齐司礼': '77DB',
#     # 深空
#     '黎深': 'LS_DB',
#     '祁煜': 'QY_DB',
#     '沈星回': 'SXH_DB',
#     '夏以昼':'XYZ_DB',
#     '秦彻': 'QC_DB',
#     'cyxTest': 'TestDB'
# }

# real_name_to_nickname = {
#     # 光夜
#     '夏鸣星': '00',
#     '萧逸': '11',
#     '查理苏': '55',
#     '陆沉': '66',
#     '齐司礼': '77',
#     # 深空
#     '黎深': 'ls',
#     '祁煜': 'qy',
#     '沈星回': 'sxh',
#     '夏以昼':'xyz',
#     '秦彻': 'qc',
# }





class InfoManager:
    """自定义信息管理器"""

    def __init__(self):

        # 根据程序路径判断男主、数据库
        current_file_path = __file__
        self.nanzhu = None
        for key in bot_paths:
            value = bot_paths.get(key)
            if value in current_file_path:
                self.nanzhu = key
                break
        if self.nanzhu == None:
            raise Exception(f'info manage 中 self.nanzhu参数错误, 请检查mongo_init.py 中男主路径是否和目前代码运行的路径不相符')

        self.client = connect_to_mongo(self.nanzhu)
        print('当前男主路径：', self.nanzhu)


        api_info = botManager.pick('charglm-api')
        self.nanzhu_real = api_info.bot_name
        print('当前男主cfg内写的真实姓名：', self.nanzhu_real)
        # nickname = real_name_to_nickname[self.nanzhu]

        self.client = connect_to_mongo(self.nanzhu)

        # 连接男主数据库
        database = self.client[db_name_to_db[self.nanzhu]]
        self.collection = database['user_info']
        self.collection_limit = database['user_limit']

        # 用户与男主对话信息
        self.collection_conversation = database['conversations']

    def update_info(self, _id: str, key: str, _value):
        """更新自定义内容"""
        """自定义内容更新形式差不多，每个都写一遍太冗杂，按照键值对更新"""
        self.collection.update_one(
            {"id": _id},
            {"$set": {key : _value}},
            upsert=True,
        )

    def get_info(self, _id) -> dict:
        """查看用户个人信息"""
        _id = str(_id)
        # 可以仅使用id查找，因为qqid基本都是数字，vx id基本都是字母 （但是可能有风险 还是后面统一排查一下）
        info = self.collection.find_one({"id": _id})
        # 这个情况是 找不到用户的购买信息，相当于还是默认用户，于是返回默认的限额
        if info is None:
            info = newInfo(self.client, user_qq= _id, db_name = self.nanzhu, info_data=None)
        return info

