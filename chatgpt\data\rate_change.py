from tinydb import TinyDB, Query

'''
本文件已停用。新文件在 github仓库：newDB
'''

def rate_change(db, num):
    # 获取默认表格
    table = db.table('_default')
    q = Query()

    # 遍历每个记录并检查id是否全部为数字字符
    for record in table.all():
        print("record:", record)
        if 'id' in record and str(record['id']).isdigit():
            print("true")

            _type = record['type']
            qq = record['id']
            new_limit = record['rate'] + num

            db.upsert({"type": _type, "id": qq, "rate": new_limit}, q.fragment({"type": _type, "id": qq}))
        else:
            print("false")

    # 关闭数据库
    db.close()



def date_change(db, new_left):
    # 获取默认表格
    table = db.table('_default')
    q = Query()

    # 遍历每个记录并检查id是否全部为数字字符
    for record in table.all():
        print("record:", record)
        if 'id' in record and str(record['id']).isdigit():
            date = record['date']
            qq = record['id']
            date_list = ["2023-10-25", "2023-10-26", "2023-10-27", "2023-10-28","2023-10-29","2023-10-30","2023-10-31","2023-11-01","2023-11-02","2023-11-03","2023-10-04"]
            if date in date_list:
                print("true")
                db.upsert({"id": qq, "left": new_left}, q.fragment({"id": qq, "date": date}))
        else:
            print("*")

    # 关闭数据库
    db.close()

# # 读取数据库文件
# rate_limit = TinyDB(r'C:\Windows-quickstart-go-cqhttp-refs.tags.55bot\chatgpt\data\rate_limit.json')
# rate_change(rate_limit, 150)
# 读取数据库文件
# date_db = TinyDB(r'C:\Windows-quickstart-go-cqhttp-refs.tags.55bot\chatgpt\data\date_start.json')
# date_change(date_db, 45)