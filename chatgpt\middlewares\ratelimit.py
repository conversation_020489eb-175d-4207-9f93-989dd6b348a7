﻿import time
from typing import Callable, Optional

from constants import config
from conversation import Convers<PERSON><PERSON><PERSON><PERSON>, ConversationContext
from manager.ratelimit import RateLimitManager
from manager.info_manage import InfoManager
from manager.mongo_init import *

from middlewares.middleware import Middleware
from constants import <PERSON>lm<PERSON><PERSON>, botManager
import re

# hashu = lambda word: ctypes.c_uint64(hash(word)).value

# 0725 cyx更改 注释掉了这些仅在mongo——init中实现

# username = "rootUser"
# password = "ChatAILover"
# auth_db = "admin"
# # self.client = MongoClient(f"mongodb://{username}:{password}@localhost:27017/{auth_db}")
# # self.client = MongoClient(f"mongodb://{username}:{password}@localhost:27017/")
# # cyx 测试路径
# client = MongoClient('mongodb://localhost:27017/')

# # 根据程序路径判断男主、数据库
# current_file_path = __file__
# nanzhu = None
# for key in bot_paths:
#     value = bot_paths.get(key)
#     if value in current_file_path:
#         nanzhu = key
#         break

#0715lsn测试
#print('当前男主：', nanzhu)

# # 连接男主数据库
# database = client[db_name_to_db[nanzhu]]
# collection = database['user_info']


manager_i = InfoManager()
nanzhu = manager_i.nanzhu
client = manager_i.client
database = client[db_name_to_db[nanzhu]]
collection = database['user_info']


manager = RateLimitManager()


class MiddlewareRatelimit(Middleware):
    def __init__(self):
        ...

    async def handle_request(self, session_id: str, prompt: str, respond: Callable,
                             conversation_context: Optional[ConversationContext], action: Callable):
        _id = session_id.split('-', 1)[1] if '-' in session_id else session_id
        rate_usage = manager.check_exceed('好友' if session_id.startswith("friend-") else '群组', _id)
        date_usage = manager.check_buy_date(_id)
        rate_limit = manager.get_limit('好友' if session_id.startswith("friend-") else '群组', _id)
        rate_limit = rate_limit['rate']     
        # 以下这些命令即使没有额度也可以执行
        if '切换' in prompt and '版' in prompt:
            switch_cmd = True
        elif 'qiV_' in prompt:
            switch_cmd = True
        elif any(item in prompt for item in ["/查询我的信息", "/查询个人信息", "/查询自定义", "/查询自定义信息", "/查看我的信息", "/查看个人信息", "/查看自定义", "/查看自定义信息"]):
            switch_cmd = True
        elif any(item in prompt for item in ["/查询额度", "/查询购买信息", "/查询购买", "/查看额度", "/查看购买信息", "/查看购买"]):
            switch_cmd = True
        elif re.compile(r"/(\d{6,12})\s?QQ迁移\s?([\w\W]{8,22})", re.IGNORECASE).match(prompt):
            switch_cmd = True
        else:
            switch_cmd = False

        conversation_handler = await ConversationHandler.get_handler(session_id)
        info = collection.find_one({"id":_id})
        if info is None:
            info = collection.find_one({"id":'default'})
        if info["version"] == 'buy1':
            user_ver = 'moonshot-api'
        elif info["version"] == 'buy2':
            user_ver = 'charglm-api'
        else:
            user_ver = 'moonshot-api'
        #conversation_context = conversation_handler.current_conversation
        #print(conversation_context)
        if conversation_context == None:
            conversation_handler.current_conversation = (
                    await conversation_handler.create(
                        user_ver
                    )
                        )
            conversation_context = conversation_handler.current_conversation
        type = conversation_context.type   
        if not switch_cmd:
            if type != 'chatglm-api':
                if rate_usage >= 1:
                    #检查使用条数是否超额
                    await respond(config.ratelimit.exceed)
                    return
                #if rate_limit != 10:
                if date_usage >= 1:
                    #检查使用天数是否超额
                    await respond(config.ratelimit.exceed_date)
                    return
                await action(session_id, prompt, conversation_context, respond)
            else:
                rate_free_usage = manager.check_free_exceed('好友' if session_id.startswith("friend-") else '群组', _id)
                if rate_free_usage >= 1:
                    #检查使用条数是否超额
                    await respond(config.ratelimit.exceed)
                    return
                await action(session_id, prompt, conversation_context, respond)
        else:
            await action(session_id, prompt, conversation_context, respond)
            

    async def handle_respond_completed(self, session_id: str, prompt: str, respond: Callable, conversation_context: Optional[ConversationContext]):
        key = '好友' if session_id.startswith("friend-") else '群组'
        msg_id = session_id.split('-', 1)[1]
        print(conversation_context)
        type = conversation_context.type
        api_info = botManager.pick(type)
        if '切换' in prompt and '版' in prompt:
            switch_cmd = True
        elif 'qiV_' in prompt:
            switch_cmd = True
        else:
            switch_cmd = False
        if not switch_cmd:
            if type != 'chatglm-api':
                amount = api_info.amount_per_msg
                manager.increment_usage(key, msg_id, amount)  
            else:
                manager.increment_free_usage(key, msg_id)
            #print("本条消息消耗"+ str(amount) + "条额度")     
