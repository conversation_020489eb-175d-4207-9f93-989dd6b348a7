# import os
# from ratelimit import *


# # 获取当前文件的完整路径
# current_file_path = __file__

# # 将路径转换为绝对路径
# absolute_path = os.path.abspath(current_file_path)

# # 获取文件所在的目录
# file_directory = os.path.dirname(absolute_path)

# # 当前文件的完整路径: D:\AILover\code\loveBot1.2\chatgpt\manager\newDBTest.py
# # 当前文件的绝对路径: D:\AILover\code\loveBot1.2\chatgpt\manager\newDBTest.py
# # 文件所在的目录: D:\AILover\code\loveBot1.2\chatgpt\manager
# # 男主的情况: D:\AILover\code\{男主名}\chatgpt\manager
# print(f"当前文件的完整路径: {current_file_path}")
# print(f"当前文件的绝对路径: {absolute_path}")
# print(f"文件所在的目录: {file_directory}")

# for key in bot_paths:
#     print(key)

# path1= bot_paths.get('11')
# print(path1)

# current_file_path = __file__
# nanzhu = None
# for key in bot_paths:
#     value = bot_paths.get(key)
#     if value in current_file_path:
#         nanzhu = key
#         break
# print('当前男主：', key)

